# File Attachments in Chatbots - Complete Implementation Guide

This guide will walk you through implementing file attachments (images and PDFs) in a chatbot using the Vercel AI SDK's `useChat` hook, with features like file previews, removal, and validation.

## Table of Contents

1. [Basic Setup](#basic-setup)
2. [File Input Implementation](#file-input-implementation)
3. [File Previews](#file-previews)
4. [File Removal](#file-removal)
5. [File Validation](#file-validation)
6. [Complete Implementation](#complete-implementation)
7. [Styling and UX](#styling-and-ux)
8. [Best Practices](#best-practices)

## Basic Setup

First, ensure you have the Vercel AI SDK installed:

```bash
pnpm install @ai-sdk/react
```

### Basic Chat Structure

```tsx
'use client';

import { useChat } from '@ai-sdk/react';
import { useRef, useState } from 'react';

export default function ChatBot() {
  const { messages, input, handleSubmit, handleInputChange, status } =
    useChat();

  return (
    <div className="chat-container">
      {/* Messages will go here */}
      {/* Input form will go here */}
    </div>
  );
}
```

## File Input Implementation

### 1. State Management

Add state for managing files and file previews:

```tsx
const [files, setFiles] = useState<FileList | undefined>(undefined);
const [filePreviews, setFilePreviews] = useState<
  { url: string; file: File; type: 'image' | 'pdf' }[]
>([]);
const fileInputRef = useRef<HTMLInputElement>(null);
```

### 2. File Selection Handler

```tsx
const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
  if (event.target.files && event.target.files.length > 0) {
    // Configuration
    const MAX_FILES = 3;
    const MAX_FILE_SIZE = 4 * 1024 * 1024; // 4MB in bytes
    const ALLOWED_TYPES = ['image/*', 'application/pdf'];

    // Check total file count
    const totalFiles = filePreviews.length + event.target.files.length;
    if (totalFiles > MAX_FILES) {
      alert(`You can upload a maximum of ${MAX_FILES} files.`);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      return;
    }

    const validFiles: File[] = [];
    let hasInvalidSize = false;
    let hasInvalidType = false;

    Array.from(event.target.files).forEach((file) => {
      // Check file size
      if (file.size > MAX_FILE_SIZE) {
        hasInvalidSize = true;
        return;
      }

      // Check file type
      if (!file.type.startsWith('image/') && file.type !== 'application/pdf') {
        hasInvalidType = true;
        return;
      }

      validFiles.push(file);
    });

    // Show error messages
    if (hasInvalidSize) {
      alert('Some files exceed 4MB limit. Please select smaller files.');
    }
    if (hasInvalidType) {
      alert('Only image and PDF files are allowed.');
    }

    // Process valid files
    if (validFiles.length > 0) {
      const dataTransfer = new DataTransfer();

      // Add existing files first
      filePreviews.forEach((preview) => {
        dataTransfer.items.add(preview.file);
      });

      // Then add new files
      validFiles.forEach((file) => {
        dataTransfer.items.add(file);
      });

      // Create previews for new files
      const newPreviews = validFiles.map((file) => ({
        url: URL.createObjectURL(file),
        file,
        type: file.type.startsWith('image/')
          ? ('image' as const)
          : ('pdf' as const),
      }));

      setFilePreviews([...filePreviews, ...newPreviews]);
      setFiles(dataTransfer.files);
    }
  }
};
```

### 3. File Input Element

```tsx
import { Paperclip } from 'lucide-react';

<input
  hidden
  type="file"
  onChange={handleFileChange}
  multiple
  ref={fileInputRef}
  accept="image/*,application/pdf"
/>

<button
  type="button"
  onClick={() => fileInputRef.current?.click()}
  disabled={filePreviews.length >= MAX_FILES}
  className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg
             transition-colors disabled:opacity-50 disabled:cursor-not-allowed
             disabled:hover:bg-transparent"
  title={filePreviews.length >= MAX_FILES ? 'Maximum files reached' : 'Attach files'}
>
  <Paperclip className="w-5 h-5" />
</button>
```

## File Previews

### Image and PDF Preview Components

```tsx
import { X, FileText } from 'lucide-react';

const FilePreview = ({
  preview,
  index,
  onRemove,
}: {
  preview: { url: string; file: File; type: 'image' | 'pdf' };
  index: number;
  onRemove: (index: number) => void;
}) => {
  return (
    <div className="relative group">
      <div className="w-20 h-20 rounded-lg overflow-hidden border-2 border-gray-200 hover:border-gray-300 transition-colors">
        {preview.type === 'image' ? (
          <img
            src={preview.url}
            alt={`Preview ${index}`}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-red-50 flex flex-col items-center justify-center">
            <FileText className="w-6 h-6 text-red-500 mb-1" />
            <span className="text-xs text-red-600 font-medium">PDF</span>
          </div>
        )}
      </div>

      {/* File name */}
      <div className="absolute -bottom-6 left-0 right-0 text-center">
        <div className="text-xs text-gray-600 truncate px-1">
          {preview.file.name}
        </div>
      </div>

      {/* Remove button */}
      <button
        type="button"
        onClick={() => onRemove(index)}
        className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 rounded-full 
                   flex items-center justify-center text-white shadow-lg transition-colors
                   opacity-0 group-hover:opacity-100"
        aria-label="Remove file"
      >
        <X className="w-3 h-3" />
      </button>
    </div>
  );
};
```

### Preview Container

```tsx
{
  filePreviews.length > 0 && (
    <div className="bg-white rounded-xl p-4 border border-slate-200 shadow-sm mb-4">
      <div className="flex items-center gap-2 mb-3">
        <Paperclip className="w-4 h-4 text-gray-500" />
        <span className="text-sm font-medium text-gray-700">
          Attached Files ({filePreviews.length}/{MAX_FILES})
        </span>
      </div>
      <div className="flex flex-wrap gap-3">
        {filePreviews.map((preview, index) => (
          <FilePreview
            key={index}
            preview={preview}
            index={index}
            onRemove={removeFile}
          />
        ))}
      </div>
    </div>
  );
}
```

## File Removal

```tsx
const removeFile = (indexToRemove: number) => {
  // Release the object URL to avoid memory leaks
  URL.revokeObjectURL(filePreviews[indexToRemove].url);

  const updatedPreviews = filePreviews.filter(
    (_, index) => index !== indexToRemove,
  );
  setFilePreviews(updatedPreviews);

  // Create a new FileList with the remaining files
  if (updatedPreviews.length === 0) {
    setFiles(undefined);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  } else {
    const dataTransfer = new DataTransfer();
    updatedPreviews.forEach((preview) => {
      dataTransfer.items.add(preview.file);
    });
    setFiles(dataTransfer.files);
  }
};
```

## File Validation

### Comprehensive Validation Function

```tsx
const validateFiles = (fileList: FileList) => {
  const MAX_FILES = 3;
  const MAX_FILE_SIZE = 4 * 1024 * 1024; // 4MB
  const ALLOWED_TYPES = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/pdf',
  ];

  const errors: string[] = [];
  const validFiles: File[] = [];

  // Check total count
  if (filePreviews.length + fileList.length > MAX_FILES) {
    errors.push(`Maximum ${MAX_FILES} files allowed`);
    return { errors, validFiles };
  }

  Array.from(fileList).forEach((file, index) => {
    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      errors.push(`${file.name}: File size exceeds 4MB limit`);
      return;
    }

    // Check file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      errors.push(`${file.name}: Only images and PDFs are allowed`);
      return;
    }

    // Check for duplicate names
    const isDuplicate = filePreviews.some(
      (preview) => preview.file.name === file.name,
    );
    if (isDuplicate) {
      errors.push(`${file.name}: File with this name already attached`);
      return;
    }

    validFiles.push(file);
  });

  return { errors, validFiles };
};
```

### File Size Helper

```tsx
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
```

## Complete Implementation

Here's a complete working component with Tailwind CSS and optional shadcn/ui components:

```tsx
'use client';

import { useChat } from '@ai-sdk/react';
import { useRef, useState, useEffect } from 'react';
import { Paperclip, X, Send, FileText, Image } from 'lucide-react';
// Optional: Import shadcn components if installed
// import { Button } from '@/components/ui/button';
// import { Input } from '@/components/ui/input';
// import { Badge } from '@/components/ui/badge';

interface FilePreview {
  url: string;
  file: File;
  type: 'image' | 'pdf';
}

interface ToastProps {
  message: string;
  type: 'error' | 'success' | 'info';
}

// Simple toast notification component
const Toast = ({ message, type }: ToastProps) => (
  <div
    className={`fixed top-4 right-4 z-50 px-4 py-2 rounded-lg shadow-lg transition-all duration-300 ${
      type === 'error'
        ? 'bg-red-500 text-white'
        : type === 'success'
          ? 'bg-green-500 text-white'
          : 'bg-blue-500 text-white'
    }`}
  >
    {message}
  </div>
);

export default function ChatBotWithAttachments() {
  const { messages, input, handleSubmit, handleInputChange, status } =
    useChat();
  const [files, setFiles] = useState<FileList | undefined>(undefined);
  const [filePreviews, setFilePreviews] = useState<FilePreview[]>([]);
  const [toast, setToast] = useState<ToastProps | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const MAX_FILES = 3;
  const MAX_FILE_SIZE = 4 * 1024 * 1024; // 4MB

  // Show toast notification
  const showToast = (message: string, type: 'error' | 'success' | 'info') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 3000);
  };

  // Format file size for display
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      const totalFiles = filePreviews.length + event.target.files.length;

      if (totalFiles > MAX_FILES) {
        showToast(`You can upload a maximum of ${MAX_FILES} files.`, 'error');
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }

      const validFiles: File[] = [];
      let hasInvalidSize = false;
      let hasInvalidType = false;

      Array.from(event.target.files).forEach((file) => {
        if (file.size > MAX_FILE_SIZE) {
          hasInvalidSize = true;
          return;
        }

        if (
          !file.type.startsWith('image/') &&
          file.type !== 'application/pdf'
        ) {
          hasInvalidType = true;
          return;
        }

        validFiles.push(file);
      });

      if (hasInvalidSize) {
        showToast(
          'Some files exceed 4MB limit. Please select smaller files.',
          'error',
        );
      }
      if (hasInvalidType) {
        showToast('Only image and PDF files are allowed.', 'error');
      }

      if (validFiles.length > 0) {
        const dataTransfer = new DataTransfer();

        filePreviews.forEach((preview) => {
          dataTransfer.items.add(preview.file);
        });

        validFiles.forEach((file) => {
          dataTransfer.items.add(file);
        });

        const newPreviews = validFiles.map((file) => ({
          url: URL.createObjectURL(file),
          file,
          type: file.type.startsWith('image/')
            ? ('image' as const)
            : ('pdf' as const),
        }));

        setFilePreviews([...filePreviews, ...newPreviews]);
        setFiles(dataTransfer.files);
        showToast(
          `${validFiles.length} file(s) added successfully!`,
          'success',
        );
      }
    }
  };

  const removeFile = (indexToRemove: number) => {
    URL.revokeObjectURL(filePreviews[indexToRemove].url);

    const updatedPreviews = filePreviews.filter(
      (_, index) => index !== indexToRemove,
    );
    setFilePreviews(updatedPreviews);

    if (updatedPreviews.length === 0) {
      setFiles(undefined);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } else {
      const dataTransfer = new DataTransfer();
      updatedPreviews.forEach((preview) => {
        dataTransfer.items.add(preview.file);
      });
      setFiles(dataTransfer.files);
    }
  };

  const handleFormSubmit = (event: React.FormEvent) => {
    event.preventDefault();

    if (!input.trim() && filePreviews.length === 0) {
      showToast('Please enter a message or attach a file.', 'error');
      return;
    }

    handleSubmit(event, {
      experimental_attachments: files,
    });

    // Clear files after submission
    setFiles(undefined);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    filePreviews.forEach((preview) => {
      URL.revokeObjectURL(preview.url);
    });
    setFilePreviews([]);
  };

  // Cleanup object URLs on unmount
  useEffect(() => {
    return () => {
      filePreviews.forEach((preview) => {
        URL.revokeObjectURL(preview.url);
      });
    };
  }, [filePreviews]);

  return (
    <div className="max-w-4xl mx-auto p-6 h-screen flex flex-col bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Toast Notification */}
      {toast && <Toast {...toast} />}

      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          AI Chat Assistant
        </h1>
        <p className="text-gray-600">
          Upload images and PDFs to get AI assistance
        </p>
      </div>

      {/* Messages Display */}
      <div className="flex-1 overflow-y-auto space-y-4 mb-6 bg-white rounded-xl shadow-sm p-4 border border-slate-200">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-slate-100 rounded-full flex items-center justify-center">
                <Paperclip className="w-8 h-8 text-slate-400" />
              </div>
              <p>Start a conversation or upload files to get started</p>
            </div>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-sm ${
                  message.role === 'user'
                    ? 'bg-blue-500 text-white ml-auto'
                    : 'bg-white text-gray-800 border border-gray-200'
                }`}
              >
                <div className="whitespace-pre-wrap">{message.content}</div>

                {/* Display attachments */}
                {message.experimental_attachments && (
                  <div className="mt-3 space-y-2">
                    {message.experimental_attachments
                      .filter((attachment) =>
                        attachment.contentType?.startsWith('image/'),
                      )
                      .map((attachment, index) => (
                        <div
                          key={`${message.id}-${index}`}
                          className="rounded-lg overflow-hidden"
                        >
                          <img
                            src={attachment.url}
                            alt={attachment.name}
                            className="max-w-full h-auto rounded-lg border border-gray-200"
                          />
                          <div className="text-xs mt-1 opacity-75">
                            {attachment.name}
                          </div>
                        </div>
                      ))}

                    {message.experimental_attachments
                      .filter(
                        (attachment) =>
                          attachment.contentType === 'application/pdf',
                      )
                      .map((attachment, index) => (
                        <div
                          key={`${message.id}-pdf-${index}`}
                          className={`flex items-center gap-2 p-2 rounded-lg ${
                            message.role === 'user'
                              ? 'bg-blue-400'
                              : 'bg-red-50 border border-red-200'
                          }`}
                        >
                          <FileText className="w-4 h-4 text-red-500" />
                          <span className="text-sm truncate">
                            {attachment.name}
                          </span>
                        </div>
                      ))}
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Input Form */}
      <form onSubmit={handleFormSubmit} className="space-y-4">
        {/* File Previews */}
        {filePreviews.length > 0 && (
          <div className="bg-white rounded-xl p-4 border border-slate-200 shadow-sm">
            <div className="flex items-center gap-2 mb-3">
              <Paperclip className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                Attached Files ({filePreviews.length}/{MAX_FILES})
              </span>
            </div>
            <div className="flex flex-wrap gap-3">
              {filePreviews.map((preview, index) => (
                <div key={index} className="relative group">
                  <div className="w-20 h-20 rounded-lg overflow-hidden border-2 border-gray-200 hover:border-gray-300 transition-colors">
                    {preview.type === 'image' ? (
                      <img
                        src={preview.url}
                        alt={`Preview ${index}`}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-red-50 flex flex-col items-center justify-center">
                        <FileText className="w-6 h-6 text-red-500 mb-1" />
                        <span className="text-xs text-red-600 font-medium">
                          PDF
                        </span>
                      </div>
                    )}
                  </div>

                  {/* File info */}
                  <div className="absolute -bottom-6 left-0 right-0 text-center">
                    <div className="text-xs text-gray-600 truncate px-1">
                      {preview.file.name}
                    </div>
                    <div className="text-xs text-gray-400">
                      {formatFileSize(preview.file.size)}
                    </div>
                  </div>

                  {/* Remove button */}
                  <button
                    type="button"
                    onClick={() => removeFile(index)}
                    className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 rounded-full 
                               flex items-center justify-center text-white shadow-lg transition-colors
                               opacity-0 group-hover:opacity-100"
                    aria-label="Remove file"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Input Controls */}
        <div className="bg-white rounded-xl border border-slate-200 shadow-sm">
          <div className="flex items-end gap-2 p-4">
            <div className="flex-1">
              <textarea
                value={input}
                placeholder="Type your message..."
                onChange={handleInputChange}
                disabled={status !== 'ready'}
                rows={1}
                className="w-full resize-none border-0 focus:ring-0 focus:outline-none placeholder-gray-500 
                           text-gray-900 text-sm leading-relaxed min-h-[40px] max-h-32"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleFormSubmit(e);
                  }
                }}
              />
            </div>

            <div className="flex items-center gap-2">
              {/* Attach Files Button */}
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                disabled={filePreviews.length >= MAX_FILES}
                className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg 
                           transition-colors disabled:opacity-50 disabled:cursor-not-allowed
                           disabled:hover:bg-transparent"
                title={
                  filePreviews.length >= MAX_FILES
                    ? 'Maximum files reached'
                    : 'Attach files'
                }
              >
                <Paperclip className="w-5 h-5" />
              </button>

              {/* Send Button */}
              <button
                type="submit"
                disabled={
                  status !== 'ready' ||
                  (!input.trim() && filePreviews.length === 0)
                }
                className="p-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white 
                           rounded-lg transition-colors disabled:cursor-not-allowed flex items-center justify-center"
              >
                <Send className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* File size info */}
          <div className="px-4 pb-2">
            <div className="text-xs text-gray-500">
              Maximum {MAX_FILES} files, 4MB each. Supports images and PDFs.
            </div>
          </div>
        </div>

        {/* Hidden File Input */}
        <input
          hidden
          type="file"
          onChange={handleFileChange}
          multiple
          ref={fileInputRef}
          accept="image/*,application/pdf"
        />
      </form>
    </div>
  );
}
```

## Styling and UX with Tailwind CSS

### Required Dependencies

Install the necessary packages for a complete implementation:

```bash
pnpm install @ai-sdk/react lucide-react
pnpm install -D tailwindcss
```

### Optional: shadcn/ui Components

For enhanced UI components, you can install shadcn/ui:

```bash
npx shadcn@latest init
npx shadcn@latest add button input textarea badge alert
```

### Tailwind Configuration

Ensure your `tailwind.config.js` includes the necessary content paths:

```js
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {},
  },
  plugins: [],
};
```

## Best Practices

### 1. Memory Management

- Always call `URL.revokeObjectURL()` when removing files or unmounting components
- Use useEffect cleanup to prevent memory leaks

### 2. User Experience

- Show file size limits clearly
- Provide immediate feedback for validation errors
- Display file names for PDFs since they can't be visually previewed
- Disable attach button when maximum files reached

### 3. Error Handling

#### Using Custom Toast (as shown in the complete example)

```tsx
interface ToastProps {
  message: string;
  type: 'error' | 'success' | 'info';
}

const Toast = ({ message, type }: ToastProps) => (
  <div
    className={`fixed top-4 right-4 z-50 px-4 py-2 rounded-lg shadow-lg transition-all duration-300 ${
      type === 'error'
        ? 'bg-red-500 text-white'
        : type === 'success'
          ? 'bg-green-500 text-white'
          : 'bg-blue-500 text-white'
    }`}
  >
    {message}
  </div>
);

const showToast = (message: string, type: 'error' | 'success' | 'info') => {
  setToast({ message, type });
  setTimeout(() => setToast(null), 3000);
};
```

#### Using shadcn/ui Toast (alternative)

```tsx
import { toast } from '@/components/ui/use-toast';

const showError = (message: string) => {
  toast({
    title: 'Error',
    description: message,
    variant: 'destructive',
  });
};

const showSuccess = (message: string) => {
  toast({
    title: 'Success',
    description: message,
  });
};
```

### 4. File Type Detection

```tsx
const getFileType = (file: File): 'image' | 'pdf' | 'unknown' => {
  if (file.type.startsWith('image/')) return 'image';
  if (file.type === 'application/pdf') return 'pdf';
  return 'unknown';
};
```

### 5. Progress Indication

For large file uploads, consider adding a progress indicator:

```tsx
const [uploadProgress, setUploadProgress] = useState<number>(0);

// Use with file upload API that supports progress tracking
const uploadWithProgress = (file: File) => {
  const formData = new FormData();
  formData.append('file', file);

  return fetch('/api/upload', {
    method: 'POST',
    body: formData,
    // Add progress tracking if your API supports it
  });
};
```

### 6. Accessibility

- Always provide alt text for images
- Use proper ARIA labels for buttons
- Ensure keyboard navigation works
- Provide screen reader friendly file descriptions

### 7. Using shadcn/ui Components (Optional Enhancement)

If you want to use shadcn/ui components for even better styling and functionality:

#### Replace Custom Components

```tsx
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Use Button instead of custom button
<Button
  type="button"
  variant="outline"
  size="sm"
  onClick={() => fileInputRef.current?.click()}
  disabled={filePreviews.length >= MAX_FILES}
>
  <Paperclip className="w-4 h-4 mr-2" />
  Attach Files
</Button>

// Use Card for file previews container
<Card className="mb-4">
  <CardHeader className="pb-3">
    <CardTitle className="text-sm flex items-center gap-2">
      <Paperclip className="w-4 h-4" />
      Attached Files ({filePreviews.length}/{MAX_FILES})
    </CardTitle>
  </CardHeader>
  <CardContent>
    <div className="flex flex-wrap gap-3">
      {/* File previews */}
    </div>
  </CardContent>
</Card>

// Use Alert for error messages
<Alert variant="destructive">
  <AlertDescription>
    Some files exceed 4MB limit. Please select smaller files.
  </AlertDescription>
</Alert>
```

### 8. Advanced Features

#### Drag and Drop Support

```tsx
const [isDragOver, setIsDragOver] = useState(false);

const handleDragOver = (e: React.DragEvent) => {
  e.preventDefault();
  setIsDragOver(true);
};

const handleDragLeave = (e: React.DragEvent) => {
  e.preventDefault();
  setIsDragOver(false);
};

const handleDrop = (e: React.DragEvent) => {
  e.preventDefault();
  setIsDragOver(false);
  const droppedFiles = e.dataTransfer.files;
  if (droppedFiles.length > 0) {
    // Process dropped files similar to handleFileChange
  }
};

// Add to your form or container
<div
  onDragOver={handleDragOver}
  onDragLeave={handleDragLeave}
  onDrop={handleDrop}
  className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
    isDragOver ? 'border-blue-400 bg-blue-50' : 'border-gray-300'
  }`}
>
  <p>Drag and drop files here, or click to select</p>
</div>;
```

#### File Upload Progress

```tsx
const [uploadProgress, setUploadProgress] = useState<Record<string, number>>(
  {},
);

// Show progress for each file
{
  filePreviews.map((preview, index) => (
    <div key={index} className="relative">
      {/* File preview */}
      {uploadProgress[preview.file.name] !== undefined && (
        <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50">
          <div
            className="h-1 bg-blue-500 transition-all duration-300"
            style={{ width: `${uploadProgress[preview.file.name]}%` }}
          />
        </div>
      )}
    </div>
  ));
}
```

This implementation provides a robust foundation for file attachments in your chatbot with proper validation, preview functionality, cleanup handling, and modern Tailwind CSS styling.
