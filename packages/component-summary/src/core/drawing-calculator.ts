/**
 * Dynamic measurement calculation utilities
 * These functions calculate measurements from drawing config data in real-time,
 * matching the logic used in LiveMeasurement.tsx but working with stored config instead of live shapes
 */

import {
  ScaleInfo,
  PaperSize,
  DynamicMeasurement,
  GeometryType,
  Drawing,
} from '../types';
import {
  getRectangleMeasurements,
  getCircleMeasurements,
  getEllipseMeasurements,
  getFreehandMeasurements,
  getFreehandSurfaceMeasurements,
  getCurveSurfaceMeasurements,
  getPointToPointMeasurements,
  getPointToPointSurfaceMeasurements,
  calculateLineLengthDirectional,
} from '../utils/distance-utils';
import { REFERENCE_CANVAS_DIMENSIONS } from '../constants';

/**
 * Calculate measurements for a single drawing from its config
 * This replicates the logic from LiveMeasurement.tsx but works with stored config
 */
export function calculateDrawingMeasurements(
  config: Record<string, string>,
  scale: ScaleInfo,
  paperSize: PaperSize,
): DynamicMeasurement | null {
  const type = config.type;

  switch (type) {
    case 'rectangle': {
      const width = parseFloat(config.width || '0');
      const height = parseFloat(config.height || '0');

      if (width === 0 || height === 0) return null;

      // Use 100% accurate directional approach
      const measurements = getRectangleMeasurements(
        width,
        height,
        scale,
        paperSize,
        REFERENCE_CANVAS_DIMENSIONS,
      );
      return {
        area: {
          value: measurements.area.realWorld,
          unit: measurements.area.unit,
          formatted: measurements.area.formatted,
        },
        perimeter: {
          value: measurements.perimeter.realWorld,
          unit: measurements.perimeter.unit,
          formatted: measurements.perimeter.formatted,
        },
      };
    }

    case 'circle': {
      const radius = parseFloat(config.radius || '0');

      if (radius === 0) return null;

      // Use 100% accurate approach
      const measurements = getCircleMeasurements(
        radius,
        scale,
        paperSize,
        REFERENCE_CANVAS_DIMENSIONS,
      );
      return {
        area: {
          value: measurements.area.realWorld,
          unit: measurements.area.unit,
          formatted: measurements.area.formatted,
        },
        circumference: {
          value: measurements.circumference.realWorld,
          unit: measurements.circumference.unit,
          formatted: measurements.circumference.formatted,
        },
      };
    }

    case 'ellipse': {
      const radiusX = parseFloat(config.radiusX || '0');
      const radiusY = parseFloat(config.radiusY || '0');

      if (radiusX === 0 || radiusY === 0) return null;

      const measurements = getEllipseMeasurements(
        radiusX,
        radiusY,
        scale,
        paperSize,
        REFERENCE_CANVAS_DIMENSIONS,
      );
      return {
        area: {
          value: measurements.area.realWorld,
          unit: measurements.area.unit,
          formatted: measurements.area.formatted,
        },
        perimeter: {
          value: measurements.perimeter.realWorld,
          unit: measurements.perimeter.unit,
          formatted: measurements.perimeter.formatted,
        },
      };
    }

    case 'freehand': {
      const points = config.points ? JSON.parse(config.points) : [];
      const closed = config.closed === 'true';

      if (points.length < 4) return null;

      if (closed) {
        // Surface component - calculate area and perimeter
        const measurements = getFreehandSurfaceMeasurements(
          points,
          scale,
          paperSize,
          REFERENCE_CANVAS_DIMENSIONS,
        );
        return {
          area: {
            value: measurements.area.realWorld,
            unit: measurements.area.unit,
            formatted: measurements.area.formatted,
          },
          perimeter: {
            value: measurements.perimeter.realWorld,
            unit: measurements.perimeter.unit,
            formatted: measurements.perimeter.formatted,
          },
        };
      } else {
        // Edge component - calculate length with directional accuracy
        const measurements = getFreehandMeasurements(
          points,
          scale,
          paperSize,
          REFERENCE_CANVAS_DIMENSIONS,
        );
        return {
          length: {
            value: measurements.length.realWorld,
            unit: measurements.length.unit,
            formatted: measurements.length.formatted,
          },
        };
      }
    }

    case 'point-to-point': {
      const points = config.points ? JSON.parse(config.points) : [];
      const closed = config.closed === 'true';

      if (points.length < 4) return null;

      if (closed) {
        // Surface component - calculate area and perimeter
        const measurements = getPointToPointSurfaceMeasurements(
          points,
          [],
          scale,
          paperSize,
          REFERENCE_CANVAS_DIMENSIONS,
        );
        return {
          area: {
            value: measurements.area.realWorld,
            unit: measurements.area.unit,
            formatted: measurements.area.formatted,
          },
          perimeter: {
            value: measurements.perimeter.realWorld,
            unit: measurements.perimeter.unit,
            formatted: measurements.perimeter.formatted,
          },
        };
      } else {
        // Edge component - calculate length with directional accuracy
        const measurements = getPointToPointMeasurements(
          points,
          [],
          scale,
          paperSize,
          REFERENCE_CANVAS_DIMENSIONS,
        );
        return {
          length: {
            value: measurements.length.realWorld,
            unit: measurements.length.unit,
            formatted: measurements.length.formatted,
          },
        };
      }
    }

    case 'curve': {
      const points = config.points ? JSON.parse(config.points) : [];
      const closed = config.closed === 'true';

      if (points.length < 4) return null;

      if (closed) {
        // Surface component - calculate area and perimeter
        const measurements = getCurveSurfaceMeasurements(
          points,
          scale,
          paperSize,
          REFERENCE_CANVAS_DIMENSIONS,
        );
        return {
          area: {
            value: measurements.area.realWorld,
            unit: measurements.area.unit,
            formatted: measurements.area.formatted,
          },
          perimeter: {
            value: measurements.perimeter.realWorld,
            unit: measurements.perimeter.unit,
            formatted: measurements.perimeter.formatted,
          },
        };
      } else {
        // Edge component - calculate length with directional accuracy
        const lengthMeasurement = calculateLineLengthDirectional(
          points,
          scale,
          paperSize,
          REFERENCE_CANVAS_DIMENSIONS,
        );

        return {
          length: {
            value: lengthMeasurement.realWorld,
            unit: lengthMeasurement.unit,
            formatted: lengthMeasurement.formatted,
          },
        };
      }
    }

    case 'point': {
      // Points don't have measurements, just position data
      return null;
    }

    default:
      return null;
  }
}

/**
 * Extract individual drawing measurement for display
 */
export function extractDrawingMeasurementDynamic(
  drawing: Drawing,
  componentType: GeometryType,
  scale: ScaleInfo,
  paperSize: PaperSize,
): { value: number; formattedValue: string; unit: string } | null {
  const measurements = calculateDrawingMeasurements(
    drawing.config,
    scale,
    paperSize,
  );

  if (!measurements) return null;

  switch (componentType) {
    case 'surface':
      if (measurements.area) {
        return {
          value: measurements.area.value,
          formattedValue: measurements.area.value.toFixed(2),
          unit: measurements.area.unit,
        };
      }
      break;
    case 'edge':
      if (measurements.length) {
        return {
          value: measurements.length.value,
          formattedValue: measurements.length.value.toFixed(2),
          unit: measurements.length.unit,
        };
      }
      break;
    case 'point':
      return null; // Points don't have measurements, just count
  }

  return null;
}
