/**
 * Utility functions for calculating real-world distances from pixel measurements
 * using the blueprint scale information
 */

import { PaperSize, ScaleInfo, Unit, DistanceMeasurement } from '../types';
import { REFERENCE_CANVAS_DIMENSIONS } from '../constants';

/**
 * PURE MATHEMATICAL APPROACH - 100% ACCURATE
 * Convert drawing pixels directly to real-world measurements without canvas intermediary
 *
 * Logic: Drawing Pixels → Paper Inches → Real World Units
 *
 * @param drawingPixels - Pixel measurement from drawing coordinates
 * @param paperSize - Physical paper dimensions
 * @param scale - Drawing scale information
 * @param canvasPixelDimensions - Canvas dimensions that represent the paper
 * @param targetUnit - Optional target unit for conversion
 */
export function drawingPixelsToRealWorldDirect(
  drawingPixels: number,
  paperSize: PaperSize,
  scale: ScaleInfo,
  canvasPixelDimensions: { width: number; height: number },
  targetUnit?: Unit,
  direction?: 'x' | 'y',
): DistanceMeasurement {
  // Step 1: Convert paper size to inches for consistent calculations
  const paperWidthInches = convertToInches(paperSize.width, paperSize.unit);
  const paperHeightInches = convertToInches(paperSize.height, paperSize.unit);

  // Step 2: Calculate pixels per inch on the drawing canvas
  const pixelsPerInchX = canvasPixelDimensions.width / paperWidthInches;
  const pixelsPerInchY = canvasPixelDimensions.height / paperHeightInches;

  // Step 3: Use appropriate direction or default to X
  const pixelsPerInch = direction === 'y' ? pixelsPerInchY : pixelsPerInchX;

  // Step 4: Convert drawing pixels to paper inches
  const paperInches = drawingPixels / pixelsPerInch;

  // Step 5: Convert paper inches to drawing units (scale numerator units)
  const drawingUnits = paperInches / convertToInches(1, scale.num_unit);

  // Step 6: Apply scale to get real-world units
  // Scale: num_metric num_unit = den_metric den_unit
  // So: drawingUnits * (den_metric / num_metric) = real world units
  const realWorldInScaleUnits =
    drawingUnits * (scale.den_metric / scale.num_metric);

  // Step 7: Convert to target unit or use scale denominator unit
  const outputUnit = targetUnit || scale.den_unit;
  const realWorldInTargetUnit = convertBetweenUnits(
    realWorldInScaleUnits,
    scale.den_unit,
    outputUnit,
  );

  return {
    pixels: drawingPixels,
    realWorld: realWorldInTargetUnit,
    unit: outputUnit,
    formatted: formatDistance(realWorldInTargetUnit, outputUnit),
  };
}

/**
 * EVEN MORE DIRECT APPROACH - BYPASS CANVAS COMPLETELY
 * If we know the exact relationship between drawing coordinates and paper dimensions
 *
 * @param drawingPixels - Pixel measurement from drawing coordinates
 * @param paperSize - Physical paper dimensions
 * @param scale - Drawing scale information
 * @param drawingCanvasSize - The size of the drawing canvas in pixels
 * @param targetUnit - Optional target unit for conversion
 */
export function drawingPixelsToRealWorldPure(
  drawingPixels: number,
  paperSize: PaperSize,
  scale: ScaleInfo,
  drawingCanvasSize: { width: number; height: number },
  targetUnit?: Unit,
  direction?: 'x' | 'y',
): DistanceMeasurement {
  // Convert paper size to inches
  const paperWidthInches = convertToInches(paperSize.width, paperSize.unit);
  const paperHeightInches = convertToInches(paperSize.height, paperSize.unit);

  // Calculate the scale factor from pixels to paper inches
  const pixelToInchX = paperWidthInches / drawingCanvasSize.width;
  const pixelToInchY = paperHeightInches / drawingCanvasSize.height;

  // Use appropriate direction
  const pixelToInch = direction === 'y' ? pixelToInchY : pixelToInchX;

  // Convert pixels to paper inches
  const paperInches = drawingPixels * pixelToInch;

  // Convert paper inches to scale numerator units
  const scaleNumeratorInches = convertToInches(
    scale.num_metric,
    scale.num_unit,
  );
  const drawingUnitsOnPaper = paperInches / scaleNumeratorInches;

  // Apply scale ratio to get real-world measurement
  const realWorldValue = drawingUnitsOnPaper * scale.den_metric;

  // Convert to target unit
  const outputUnit = targetUnit || scale.den_unit;
  const realWorldInTargetUnit = convertBetweenUnits(
    realWorldValue,
    scale.den_unit,
    outputUnit,
  );

  return {
    pixels: drawingPixels,
    realWorld: realWorldInTargetUnit,
    unit: outputUnit,
    formatted: formatDistance(realWorldInTargetUnit, outputUnit),
  };
}

/**
 * Calculate distance between two points
 */
export function calculateDistance(
  point1: { x: number; y: number },
  point2: { x: number; y: number },
): number {
  const dx = point2.x - point1.x;
  const dy = point2.y - point1.y;
  return Math.sqrt(dx * dx + dy * dy);
}

/**
 * Calculate perimeter of a rectangle
 */
export function calculateRectanglePerimeter(
  width: number,
  height: number,
): number {
  return 2 * (Math.abs(width) + Math.abs(height));
}

/**
 * Calculate area of a rectangle
 */
export function calculateRectangleArea(width: number, height: number): number {
  return Math.abs(width) * Math.abs(height);
}

/**
 * Calculate circumference of a circle
 */
export function calculateCircleCircumference(radius: number): number {
  return 2 * Math.PI * Math.abs(radius);
}

/**
 * Calculate area of a circle
 */
export function calculateCircleArea(radius: number): number {
  return Math.PI * Math.abs(radius) * Math.abs(radius);
}

/**
 * Calculate perimeter of an ellipse (Ramanujan's approximation)
 */
export function calculateEllipsePerimeter(
  radiusX: number,
  radiusY: number,
): number {
  const a = Math.abs(radiusX);
  const b = Math.abs(radiusY);
  const h = Math.pow((a - b) / (a + b), 2);
  return Math.PI * (a + b) * (1 + (3 * h) / (10 + Math.sqrt(4 - 3 * h)));
}

/**
 * Calculate area of an ellipse
 */
export function calculateEllipseArea(radiusX: number, radiusY: number): number {
  return Math.PI * Math.abs(radiusX) * Math.abs(radiusY);
}

/**
 * Convert between different units
 */
function convertBetweenUnits(
  value: number,
  fromUnit: Unit,
  toUnit: Unit,
): number {
  if (fromUnit === toUnit) {
    return value;
  }

  // Convert to cm first, then to target unit
  const valueInCm = convertToCm(value, fromUnit);
  return convertFromCm(valueInCm, toUnit);
}

/**
 * Convert distance to inches
 */
function convertToInches(value: number, unit: Unit): number {
  switch (unit) {
    case 'inch':
      return value;
    case 'cm':
      return value / 2.54; // 1 inch = 2.54 cm
    case 'ft':
      return value * 12; // 1 foot = 12 inches
    default:
      return value;
  }
}

/**
 * Convert distance to cm
 */
function convertToCm(value: number, unit: Unit): number {
  switch (unit) {
    case 'cm':
      return value;
    case 'inch':
      return value * 2.54; // 1 inch = 2.54 cm
    case 'ft':
      return value * 30.48; // 1 foot = 30.48 cm
    default:
      return value;
  }
}

/**
 * Convert distance from cm to target unit
 */
function convertFromCm(valueCm: number, targetUnit: Unit): number {
  switch (targetUnit) {
    case 'cm':
      return valueCm;
    case 'inch':
      return valueCm / 2.54;
    case 'ft':
      return valueCm / 30.48;
    default:
      return valueCm;
  }
}

/**
 * Format distance for display
 */
function formatDistance(value: number, unit: string): string {
  // Round to 2 decimal places for display
  const rounded = Math.round(value * 100) / 100;
  return `${rounded} ${unit}`;
}

/**
 * Get measurements for a rectangle shape using 100% accurate directional approach
 */
export function getRectangleMeasurements(
  width: number,
  height: number,
  scale: ScaleInfo,
  paperSize: PaperSize,
  canvasSize: { width: number; height: number } = REFERENCE_CANVAS_DIMENSIONS,
): {
  width: DistanceMeasurement;
  height: DistanceMeasurement;
  perimeter: DistanceMeasurement;
  area: { pixels: number; realWorld: number; unit: string; formatted: string };
} {
  // Use pure mathematical approach with directional accuracy
  const widthMeasurement = drawingPixelsToRealWorldPure(
    Math.abs(width),
    paperSize,
    scale,
    canvasSize,
    undefined,
    'x',
  );
  const heightMeasurement = drawingPixelsToRealWorldPure(
    Math.abs(height),
    paperSize,
    scale,
    canvasSize,
    undefined,
    'y',
  );
  const perimeterPixels = calculateRectanglePerimeter(width, height);
  const perimeterMeasurement = drawingPixelsToRealWorldPure(
    perimeterPixels,
    paperSize,
    scale,
    canvasSize,
    undefined,
    'x',
  );

  // Area calculation (square units)
  const areaPixels = calculateRectangleArea(width, height);
  const areaRealWorld =
    widthMeasurement.realWorld * heightMeasurement.realWorld;

  return {
    width: widthMeasurement,
    height: heightMeasurement,
    perimeter: perimeterMeasurement,
    area: {
      pixels: areaPixels,
      realWorld: areaRealWorld,
      unit: `sq ${widthMeasurement.unit}`,
      formatted: `${Math.round(areaRealWorld * 100) / 100} sq ${widthMeasurement.unit}`,
    },
  };
}

/**
 * Calculate total length of a freehand line from points array
 */
export function calculateLineLength(points: number[]): number {
  let totalLength = 0;
  for (let i = 0; i < points.length - 2; i += 2) {
    const dx = points[i + 2] - points[i];
    const dy = points[i + 3] - points[i + 1];
    totalLength += Math.sqrt(dx * dx + dy * dy);
  }
  return totalLength;
}

/**
 * Detect if a line is primarily horizontal or vertical
 * Returns 'x' for horizontal, 'y' for vertical, or null for diagonal
 */
export function detectLineDirection(points: number[]): 'x' | 'y' | null {
  if (points.length < 4) return null;

  // Calculate total displacement
  const startX = points[0];
  const startY = points[1];
  const endX = points[points.length - 2];
  const endY = points[points.length - 1];

  const totalDx = Math.abs(endX - startX);
  const totalDy = Math.abs(endY - startY);

  // If one direction is significantly larger, consider it the primary direction
  const threshold = 2; // Line must be at least 2x longer in one direction

  if (totalDx > totalDy * threshold) {
    return 'x'; // Primarily horizontal
  } else if (totalDy > totalDx * threshold) {
    return 'y'; // Primarily vertical
  }

  return null; // Diagonal or roughly equal
}

/**
 * Calculate line length with directional accuracy for single lines
 */
export function calculateLineLengthDirectional(
  points: number[],
  scale: ScaleInfo,
  paperSize: PaperSize,
  canvasSize: { width: number; height: number } = REFERENCE_CANVAS_DIMENSIONS,
): DistanceMeasurement {
  const lengthPixels = calculateLineLength(points);
  const direction = detectLineDirection(points);

  // Use pure mathematical approach with detected direction
  return drawingPixelsToRealWorldPure(
    lengthPixels,
    paperSize,
    scale,
    canvasSize,
    undefined,
    direction || 'x', // Default to X if direction can't be determined
  );
}

/**
 * Get measurements for a freehand line using 100% accurate directional approach
 */
export function getFreehandMeasurements(
  points: number[],
  scale: ScaleInfo,
  paperSize: PaperSize,
  canvasSize: { width: number; height: number } = REFERENCE_CANVAS_DIMENSIONS,
): {
  length: DistanceMeasurement;
} {
  const lengthMeasurement = calculateLineLengthDirectional(
    points,
    scale,
    paperSize,
    canvasSize,
  );

  return {
    length: lengthMeasurement,
  };
}

/**
 * Get measurements for a circle shape using 100% accurate approach
 */
export function getCircleMeasurements(
  radius: number,
  scale: ScaleInfo,
  paperSize: PaperSize,
  canvasSize: { width: number; height: number } = REFERENCE_CANVAS_DIMENSIONS,
): {
  radius: DistanceMeasurement;
  diameter: DistanceMeasurement;
  circumference: DistanceMeasurement;
  area: { pixels: number; realWorld: number; unit: string; formatted: string };
} {
  // Use average of X and Y directions for circular measurements since circles are symmetric
  const radiusMeasurement = drawingPixelsToRealWorldPure(
    Math.abs(radius),
    paperSize,
    scale,
    canvasSize,
  );
  const diameterMeasurement = drawingPixelsToRealWorldPure(
    Math.abs(radius * 2),
    paperSize,
    scale,
    canvasSize,
  );
  const circumferencePixels = calculateCircleCircumference(radius);
  const circumferenceMeasurement = drawingPixelsToRealWorldPure(
    circumferencePixels,
    paperSize,
    scale,
    canvasSize,
  );

  // Area calculation (square units)
  const areaPixels = calculateCircleArea(radius);
  const areaRealWorld = Math.PI * radiusMeasurement.realWorld ** 2;

  return {
    radius: radiusMeasurement,
    diameter: diameterMeasurement,
    circumference: circumferenceMeasurement,
    area: {
      pixels: areaPixels,
      realWorld: areaRealWorld,
      unit: `sq ${radiusMeasurement.unit}`,
      formatted: `${Math.round(areaRealWorld * 100) / 100} sq ${radiusMeasurement.unit}`,
    },
  };
}

/**
 * Get measurements for an ellipse shape using 100% accurate directional approach
 */
export function getEllipseMeasurements(
  radiusX: number,
  radiusY: number,
  scale: ScaleInfo,
  paperSize: PaperSize,
  canvasSize: { width: number; height: number } = REFERENCE_CANVAS_DIMENSIONS,
): {
  radiusX: DistanceMeasurement;
  radiusY: DistanceMeasurement;
  perimeter: DistanceMeasurement;
  area: { pixels: number; realWorld: number; unit: string; formatted: string };
} {
  // Use directional accuracy: radiusX uses X direction, radiusY uses Y direction
  const radiusXMeasurement = drawingPixelsToRealWorldPure(
    Math.abs(radiusX),
    paperSize,
    scale,
    canvasSize,
    undefined,
    'x',
  );
  const radiusYMeasurement = drawingPixelsToRealWorldPure(
    Math.abs(radiusY),
    paperSize,
    scale,
    canvasSize,
    undefined,
    'y',
  );
  const perimeterPixels = calculateEllipsePerimeter(radiusX, radiusY);
  // For perimeter, use X direction as it's typically more accurate
  const perimeterMeasurement = drawingPixelsToRealWorldPure(
    perimeterPixels,
    paperSize,
    scale,
    canvasSize,
    undefined,
    'x',
  );

  // Area calculation (square units)
  const areaPixels = calculateEllipseArea(radiusX, radiusY);
  const areaRealWorld =
    Math.PI * radiusXMeasurement.realWorld * radiusYMeasurement.realWorld;

  return {
    radiusX: radiusXMeasurement,
    radiusY: radiusYMeasurement,
    perimeter: perimeterMeasurement,
    area: {
      pixels: areaPixels,
      realWorld: areaRealWorld,
      unit: `sq ${radiusXMeasurement.unit}`,
      formatted: `${Math.round(areaRealWorld * 100) / 100} sq ${radiusXMeasurement.unit}`,
    },
  };
}

/**
 * Calculate area of a closed polygon using the shoelace formula
 */
export function calculatePolygonArea(points: number[]): number {
  if (points.length < 6) return 0; // Need at least 3 points (6 coordinates)

  let area = 0;
  const n = points.length / 2;

  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n;
    const xi = points[i * 2];
    const yi = points[i * 2 + 1];
    const xj = points[j * 2];
    const yj = points[j * 2 + 1];

    area += xi * yj - xj * yi;
  }

  return Math.abs(area) / 2;
}

/**
 * Calculate area of a polygon using real-world coordinates (shoelace formula)
 * This converts each point to real-world coordinates first, then calculates area
 * Uses the same directional approach as the working line calculations
 */
function calculatePolygonAreaInRealWorld(
  points: number[],
  paperSize: PaperSize,
  scale: ScaleInfo,
  canvasSize: { width: number; height: number } = REFERENCE_CANVAS_DIMENSIONS,
): number {
  if (points.length < 6) return 0; // Need at least 3 points (6 coordinates)

  // Convert all points to real-world coordinates using the same approach as line calculations
  const realWorldPoints: number[] = [];
  for (let i = 0; i < points.length; i += 2) {
    const x = points[i];
    const y = points[i + 1];

    // Convert X coordinate using X direction scaling (same as working line calculations)
    const xMeasurement = drawingPixelsToRealWorldPure(
      x,
      paperSize,
      scale,
      canvasSize,
      undefined,
      'x',
    );

    // Convert Y coordinate using Y direction scaling (same as working line calculations)
    const yMeasurement = drawingPixelsToRealWorldPure(
      y,
      paperSize,
      scale,
      canvasSize,
      undefined,
      'y',
    );

    realWorldPoints.push(xMeasurement.realWorld, yMeasurement.realWorld);
  }

  // Apply shoelace formula to real-world coordinates
  let area = 0;
  const n = realWorldPoints.length / 2;

  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n;
    const xi = realWorldPoints[i * 2];
    const yi = realWorldPoints[i * 2 + 1];
    const xj = realWorldPoints[j * 2];
    const yj = realWorldPoints[j * 2 + 1];

    area += xi * yj - xj * yi;
  }

  return Math.abs(area) / 2;
}

/**
 * Get measurements for a freehand surface (surface component)
 */
export function getFreehandSurfaceMeasurements(
  points: number[],
  scale: ScaleInfo,
  paperSize: PaperSize,
  canvasSize: { width: number; height: number } = REFERENCE_CANVAS_DIMENSIONS,
): {
  perimeter: DistanceMeasurement;
  area: { pixels: number; realWorld: number; unit: string; formatted: string };
} {
  // Calculate perimeter (including closing line back to start)
  const perimeterPixels =
    calculateLineLength(points) +
    Math.sqrt(
      Math.pow(points[0] - points[points.length - 2], 2) +
        Math.pow(points[1] - points[points.length - 1], 2),
    );

  // Use directional accuracy for perimeter - use average since perimeter involves both directions
  const perimeterMeasurement = drawingPixelsToRealWorldPure(
    perimeterPixels,
    paperSize,
    scale,
    canvasSize,
  );

  // Calculate area using the same approach as working line calculations
  // Convert each point to real-world coordinates first, then apply shoelace formula
  const areaPixels = calculatePolygonArea(points);
  const areaRealWorld = calculatePolygonAreaInRealWorld(
    points,
    paperSize,
    scale,
    canvasSize,
  );

  return {
    perimeter: perimeterMeasurement,
    area: {
      pixels: areaPixels,
      realWorld: areaRealWorld,
      unit: `sq ${perimeterMeasurement.unit}`,
      formatted: `${Math.round(areaRealWorld * 100) / 100} sq ${perimeterMeasurement.unit}`,
    },
  };
}

/**
 * Get measurements for a curve surface (surface component)
 */
export function getCurveSurfaceMeasurements(
  points: number[],
  scale: ScaleInfo,
  paperSize: PaperSize,
  canvasSize: { width: number; height: number } = REFERENCE_CANVAS_DIMENSIONS,
): {
  perimeter: DistanceMeasurement;
  area: { pixels: number; realWorld: number; unit: string; formatted: string };
} {
  // For curves, we need to calculate the perimeter along the curve path
  // Since curves use tension, we approximate by calculating the length of the curve
  // including the closing line back to start for closed curves
  const perimeterPixels =
    calculateLineLength(points) +
    Math.sqrt(
      Math.pow(points[0] - points[points.length - 2], 2) +
        Math.pow(points[1] - points[points.length - 1], 2),
    );

  // Use directional accuracy for perimeter - use average since perimeter involves both directions
  const perimeterMeasurement = drawingPixelsToRealWorldPure(
    perimeterPixels,
    paperSize,
    scale,
    canvasSize,
  );

  // Calculate area using the same approach as other surface calculations
  // For curves, we use the anchor points to approximate the area
  const areaPixels = calculatePolygonArea(points);
  const areaRealWorld = calculatePolygonAreaInRealWorld(
    points,
    paperSize,
    scale,
    canvasSize,
  );

  return {
    perimeter: perimeterMeasurement,
    area: {
      pixels: areaPixels,
      realWorld: areaRealWorld,
      unit: `sq ${perimeterMeasurement.unit}`,
      formatted: `${Math.round(areaRealWorld * 100) / 100} sq ${perimeterMeasurement.unit}`,
    },
  };
}

/**
 * Get measurements for a point-to-point line using 100% accurate directional approach
 */
export function getPointToPointMeasurements(
  points: number[],
  previewPoints: number[],
  scale: ScaleInfo,
  paperSize: PaperSize,
  canvasSize: { width: number; height: number } = REFERENCE_CANVAS_DIMENSIONS,
): {
  length: DistanceMeasurement;
} {
  // Use preview points if available (during drawing), otherwise use confirmed points
  const pointsToUse = previewPoints.length > 0 ? previewPoints : points;

  const lengthMeasurement = calculateLineLengthDirectional(
    pointsToUse,
    scale,
    paperSize,
    canvasSize,
  );

  return {
    length: lengthMeasurement,
  };
}

/**
 * Get measurements for a point-to-point surface (surface component)
 */
export function getPointToPointSurfaceMeasurements(
  points: number[],
  previewPoints: number[],
  scale: ScaleInfo,
  paperSize: PaperSize,
  canvasSize: { width: number; height: number } = REFERENCE_CANVAS_DIMENSIONS,
): {
  perimeter: DistanceMeasurement;
  area: { pixels: number; realWorld: number; unit: string; formatted: string };
} {
  // Use preview points if available (during drawing), otherwise use confirmed points
  const pointsToUse = previewPoints.length > 0 ? previewPoints : points;

  // For surface components, we need to close the shape for proper measurement
  // If preview points don't already include closing line, we need to calculate it
  const closedPoints = [...pointsToUse];

  // Check if the shape is already closed (last point equals first point)
  const isAlreadyClosed =
    pointsToUse.length >= 4 &&
    pointsToUse[0] === pointsToUse[pointsToUse.length - 2] &&
    pointsToUse[1] === pointsToUse[pointsToUse.length - 1];

  if (!isAlreadyClosed && pointsToUse.length >= 4) {
    // Add closing line back to start for perimeter calculation
    closedPoints.push(pointsToUse[0], pointsToUse[1]);
  }

  // Calculate perimeter using the closed points with directional accuracy
  const perimeterPixels = calculateLineLength(closedPoints);
  const perimeterMeasurement = drawingPixelsToRealWorldPure(
    perimeterPixels,
    paperSize,
    scale,
    canvasSize,
  );

  // Calculate area using the same approach as working line calculations
  // Convert each point to real-world coordinates first, then apply shoelace formula
  const areaPixels = calculatePolygonArea(pointsToUse);
  const areaRealWorld = calculatePolygonAreaInRealWorld(
    pointsToUse,
    paperSize,
    scale,
    canvasSize,
  );

  return {
    perimeter: perimeterMeasurement,
    area: {
      pixels: areaPixels,
      realWorld: areaRealWorld,
      unit: `sq ${perimeterMeasurement.unit}`,
      formatted: `${Math.round(areaRealWorld * 100) / 100} sq ${perimeterMeasurement.unit}`,
    },
  };
}
