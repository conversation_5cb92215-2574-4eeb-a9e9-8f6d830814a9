# Curve Surface Component Implementation Plan

## Current State Analysis

### How Curves Currently Work (Edge Components Only)
1. **Type Definition**: `CurveShape` in `drawing-types.ts` - missing `closed` property
2. **Drawing Initialization**: In `useDrawingTools.ts`, curves are hardcoded with `fill: DRAWING_COLORS.TRANSPARENT_FILL`
3. **Rendering**: In `DrawingLayer.tsx`, curves are hardcoded with `closed={false}` (lines 299, 518)
4. **Storage**: In `useDrawingTools.ts` and `measurement-utils.tsx`, curves are hardcoded with `closed: 'false'` (lines 705, 112, 1059)
5. **Parsing**: In `drawing-measurements.ts`, curves are parsed without a `closed` property
6. **Measurements**: In `drawing-calculator.ts`, curves only calculate length (edge component logic)

### How Surface Components Work (Freehand & Point-to-Point)
1. **Determination**: Based on `selectedComponentItem?.geometryType === 'surface'`
2. **Fill Color**: Surface components get semi-transparent fill color (`${color}80`)
3. **Closed Property**: Set to `true` for surface, `false` for edge
4. **Measurements**: Surface components calculate both area and perimeter
5. **Validation**: Surface components check for self-intersection

## Implementation Steps

### 1. Update Type Definition
**File**: `apps/frontend/src/modules/takeoff/types/drawing-types.ts`
- Add `closed: boolean` property to `CurveShape` type (line 45-52)

### 2. Update Curve Drawing Initialization
**File**: `apps/frontend/src/modules/takeoff/hooks/useDrawingTools.ts`
- Modify curve tool initialization (lines 332-357) to:
  - Determine if it should be surface or edge based on `selectedComponentItem?.geometryType`
  - Set appropriate fill color (transparent for edge, semi-transparent for surface)
  - Add `closed` property to the curve shape

### 3. Update Curve Rendering Logic
**File**: `apps/frontend/src/modules/takeoff/components/DrawingLayer.tsx`
- Update saved curve rendering (lines 279-301) to use `config.closed` instead of hardcoded `false`
- Update current drawing curve rendering (lines 489-548) to use `shape.closed` instead of hardcoded `false`
- Add fill logic for closed curves (similar to freehand on lines 477-481)

### 4. Update Curve Storage Logic
**File**: `apps/frontend/src/modules/takeoff/hooks/useDrawingTools.ts`
- Update curve saving in `handleCanvasClick` (lines 693-711) to use `currentDrawing.closed` instead of hardcoded `'false'`
- Update curve saving in `saveCurrentDrawing` (lines 1056-1062) to use `currentDrawing.closed` instead of hardcoded `'false'`

**File**: `apps/frontend/src/modules/takeoff/utils/measurement-utils.tsx`
- Update curve config generation (lines 109-116) to use `drawing.closed` instead of hardcoded `'false'`

### 5. Update Curve Parsing Logic
**File**: `apps/frontend/src/modules/takeoff/utils/drawing-measurements.ts`
- Update curve parsing (lines 97-114) to include `closed: config.closed === 'true'`

### 6. Implement Curve Surface Measurements
**File**: `packages/component-summary/src/core/drawing-calculator.ts`
- Update curve case (lines 209-229) to:
  - Check if curve is closed (surface) or open (edge)
  - For closed curves: calculate area and perimeter using new `getCurveSurfaceMeasurements` function
  - For open curves: keep existing length calculation

**File**: `packages/component-summary/src/utils/distance-utils.ts`
- Add new `getCurveSurfaceMeasurements` function similar to `getFreehandSurfaceMeasurements`
- This function should calculate area and perimeter for closed curves

### 7. Add Curve Surface Validation
**File**: `apps/frontend/src/modules/takeoff/hooks/useDrawingTools.ts`
- Add self-intersection check for surface curves (similar to freehand and point-to-point)
- Add this check in the curve completion logic (around line 687)

### 8. Update Curve Measurement Display
**File**: `apps/frontend/src/modules/takeoff/utils/drawing-measurements.ts`
- Update curve measurement display logic (lines 283-295) to:
  - Show area and perimeter for closed curves (surface)
  - Show length for open curves (edge)

## Key Implementation Details

### Surface vs Edge Determination
```typescript
// In curve tool initialization
let isSurface = selectedComponentItem?.geometryType === 'surface';

// If in measure mode, use measure tool properties
if (isMeasureMode) {
  const measureProps = getMeasureToolProperties(selectedMeasureTool);
  isSurface = measureProps.isSurface;
}
```

### Fill Color Logic
```typescript
const shapeFillColor = isSurface && selectedComponentItem?.color
  ? `${selectedComponentItem.color}80`
  : isMeasureMode
    ? DRAWING_COLORS.MEASURE_FILL
    : DRAWING_COLORS.TRANSPARENT_FILL;
```

### Rendering Logic
```typescript
// In DrawingLayer.tsx
closed={parseBoolean(config.closed)} // For saved drawings
closed={shape.closed} // For current drawing

// Fill logic
fill={shape.closed ? makeLighterForDisplay(shape.fill) : 'transparent'}
```

### Measurement Logic
```typescript
// In drawing-calculator.ts
case 'curve': {
  const points = config.points ? JSON.parse(config.points) : [];
  const closed = config.closed === 'true';
  
  if (points.length < 4) return null;
  
  if (closed) {
    // Surface component - calculate area and perimeter
    const measurements = getCurveSurfaceMeasurements(points, scale, paperSize, REFERENCE_CANVAS_DIMENSIONS);
    return {
      area: { value: measurements.area.realWorld, unit: measurements.area.unit, formatted: measurements.area.formatted },
      perimeter: { value: measurements.perimeter.realWorld, unit: measurements.perimeter.unit, formatted: measurements.perimeter.formatted }
    };
  } else {
    // Edge component - calculate length
    const lengthMeasurement = calculateLineLengthDirectional(points, scale, paperSize, REFERENCE_CANVAS_DIMENSIONS);
    return {
      length: { value: lengthMeasurement.realWorld, unit: lengthMeasurement.unit, formatted: lengthMeasurement.formatted }
    };
  }
}
```

## Files to Modify

1. `apps/frontend/src/modules/takeoff/types/drawing-types.ts`
2. `apps/frontend/src/modules/takeoff/hooks/useDrawingTools.ts`
3. `apps/frontend/src/modules/takeoff/components/DrawingLayer.tsx`
4. `apps/frontend/src/modules/takeoff/utils/measurement-utils.tsx`
5. `apps/frontend/src/modules/takeoff/utils/drawing-measurements.ts`
6. `packages/component-summary/src/core/drawing-calculator.ts`
7. `packages/component-summary/src/utils/distance-utils.ts`

## Testing Considerations

1. Test curve creation with surface components (should be closed with fill)
2. Test curve creation with edge components (should be open without fill)
3. Test curve measurements for both surface and edge modes
4. Test curve rendering for both saved and current drawings
5. Test self-intersection validation for surface curves
6. Test measure mode with curves
