import {
  IsString,
  IsEnum,
  IsOptional,
  IsArray,
  ValidateNested,
  IsDateString,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';

// Simplified DTO that matches Vercel AI SDK UIMessage format
export class UIMessageDto {
  @IsOptional()
  @IsString()
  id?: string;

  @IsString()
  role: string;

  @IsString()
  content: string;

  @IsOptional()
  createdAt?: any;

  @IsOptional()
  data?: any;

  @IsOptional()
  parts?: any[];
}

export class ChatMessageDto {
  @IsString()
  id: string;

  @IsEnum(['user', 'assistant'])
  role: 'user' | 'assistant';

  @IsString()
  content: string;

  @IsDateString()
  timestamp: string;

  @IsOptional()
  @IsEnum(['blueprint', 'drawings'])
  contextType?: 'blueprint' | 'drawings';
}

export class BlueprintContextDto {
  @IsString()
  imageUrl: string;

  @IsString()
  fileName: string;

  @IsString()
  takeoffId: string;

  @IsString()
  blueprintFileId: string;

  @IsString()
  blueprintImageId: string;
}

export class DrawingDto {
  @IsString()
  id: string;

  @IsString()
  blueprintImageId: string;

  @IsOptional()
  componentId?: number;

  @IsOptional()
  config?: Record<string, any>;

  @IsOptional()
  @IsDateString()
  createdAt?: string;

  @IsOptional()
  @IsDateString()
  updatedAt?: string;
}

export class DrawingsContextDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DrawingDto)
  drawings: DrawingDto[];

  @IsString()
  imageUrl: string;

  @IsString()
  takeoffId: string;

  @IsString()
  blueprintImageId: string;
}

export class ChatRequestDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UIMessageDto)
  messages: UIMessageDto[];

  @IsOptional()
  @IsObject()
  data?: any;

  @IsOptional()
  @IsEnum(['openai', 'google', 'anthropic'])
  model?: 'openai' | 'google' | 'anthropic';

  @IsOptional()
  @IsString()
  systemPrompt?: string;
}
