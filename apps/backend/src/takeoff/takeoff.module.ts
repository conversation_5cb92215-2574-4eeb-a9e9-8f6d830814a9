import { Module } from '@nestjs/common';
import { TakeoffService } from './takeoff.service';
import { TakeoffController } from './takeoff.controller';
import { BlueprintFilesModule } from 'src/blueprint-files/blueprint-files.module';
@Module({
  imports: [BlueprintFilesModule],
  controllers: [TakeoffController],
  providers: [TakeoffService],
  exports: [TakeoffService],
})
export class TakeoffModule {}
