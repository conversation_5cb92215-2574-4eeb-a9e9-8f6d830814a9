import {
  Layer,
  Rect,
  Text,
  Circle as KonvaCircle,
  Ellipse as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Arrow,
} from 'react-konva';
import Konva from 'konva';
import { DrawingShape } from '../types/drawing-types';
import { CanvasDimensions } from '../types/drawing-types';
import { Drawing } from '../types/drawing';
import {
  makeLighterForDisplay,
  makeBrighterForBorder,
} from '../utils/canvas-utils';
import { useTakeoffStore } from '../store/takeoff-store';
import { SelectionBounds } from './SelectionBounds';
import { LiveMeasurement } from './LiveMeasurement';
import { PersistentMeasurement } from './PersistentMeasurement';
import { CommentAnnotation } from './CommentAnnotation';
import { ARROW_POINTER } from '../constants/drawing';

interface DrawingLayerProps {
  fetchedDrawings?: Drawing[];
  drawingsError: any;
  currentDrawing: DrawingShape | null;
  dimensions: CanvasDimensions;
  zoom: number;
  selectedTool: string;
  setIsShapeDragging: React.Dispatch<React.SetStateAction<boolean>>;
  groupDragOffset?: { x: number; y: number } | null;
  handleDragEnd: (
    drawingId: number,
    event: Konva.KonvaEventObject<DragEvent>,
  ) => void;
  handleGroupDragStart: () => void;
  handleGroupDragMove: (deltaX: number, deltaY: number) => void;
  handleGroupDragEnd: (deltaX: number, deltaY: number) => void;
  handleDrawingSelect: (drawingId: number) => void;
  handleDrawingContextMenu?: (
    drawing: Drawing,
    e: Konva.KonvaEventObject<PointerEvent>,
  ) => void;
  handleSelectionBoundsContextMenu?: (
    e: any,
    bounds: { x: number; y: number; width: number; height: number },
  ) => void;
  handleDrawingHover?: (
    drawing: Drawing,
    e: Konva.KonvaEventObject<MouseEvent>,
  ) => void;
  handleDrawingMouseMove?: (
    drawing: Drawing,
    e: Konva.KonvaEventObject<MouseEvent>,
  ) => void;
  handleDrawingHoverEnd?: () => void;
}

/**
 * Layer component for displaying drawings (both saved and in-progress)
 */
export function DrawingLayer({
  fetchedDrawings = [],
  drawingsError,
  currentDrawing,
  dimensions,
  zoom,
  selectedTool,
  setIsShapeDragging,
  groupDragOffset,
  handleDragEnd,
  handleGroupDragStart,
  handleGroupDragMove,
  handleGroupDragEnd,
  handleDrawingSelect,
  handleDrawingContextMenu,
  handleSelectionBoundsContextMenu,
  handleDrawingHover,
  handleDrawingMouseMove,
  handleDrawingHoverEnd,
}: DrawingLayerProps) {
  // Get selected drawing ID and multi-selection from store
  const {
    selectedDrawingId,
    selectedDrawingIds,
    selectedImage,
    isEditMode,
    isCanvasLoaded,
    locatedComponentId,
    paperSize,
    hiddenComponentId,
  } = useTakeoffStore();
  // Filter drawings before mapping to hide drawings of hidden component
  const visibleDrawings =
    fetchedDrawings?.filter(
      (drawing) =>
        hiddenComponentId === null ||
        drawing.component?.id !== hiddenComponentId,
    ) || [];

  return (
    <Layer>
      {/* Error state */}
      {drawingsError && (
        <Text
          text={`Error loading drawings: ${drawingsError.message}`}
          x={dimensions.width / 2 - 150}
          y={dimensions.height / 2 - 10}
          fill="red"
          fontSize={16}
        />
      )}

      <>
        {isCanvasLoaded &&
          !drawingsError &&
          visibleDrawings.map((apiDrawing) => {
            const { config, component, id: apiId } = apiDrawing;

            if (
              config.type !== 'rectangle' &&
              config.type !== 'circle' &&
              config.type !== 'ellipse' &&
              config.type !== 'freehand' &&
              config.type !== 'curve' &&
              config.type !== 'point-to-point' &&
              config.type !== 'arrow' &&
              config.type !== 'point' &&
              config.type !== 'comment'
            ) {
              console.warn(
                'Unknown shape type from API config:',
                config.type,
                apiDrawing,
              );
              return null;
            }

            const isSelected =
              selectedDrawingId === apiDrawing.id ||
              selectedDrawingIds.includes(apiDrawing.id);

            // Check if this drawing belongs to the located component
            const isLocatedComponent =
              locatedComponentId &&
              apiDrawing.componentId === locatedComponentId;

            // Disable individual dragging when multiple drawings are selected (to enable group dragging)
            const isIndividualDragDisabled =
              selectedDrawingIds.length > 1 && selectedTool === 'select';

            // Apply group drag offset for real-time visual feedback
            const baseX = parseFloat(config.x || '0');
            const baseY = parseFloat(config.y || '0');
            const isInGroupDrag =
              groupDragOffset &&
              selectedDrawingIds.includes(apiDrawing.id) &&
              selectedDrawingIds.length > 1;
            const visualX = isInGroupDrag ? baseX + groupDragOffset.x : baseX;
            const visualY = isInGroupDrag ? baseY + groupDragOffset.y : baseY;

            const commonShapeProps = {
              x: visualX,
              y: visualY,
              fill: isLocatedComponent
                ? makeLighterForDisplay('#ff0000') // Red background for located component drawings
                : makeLighterForDisplay(
                    component?.color || config.fill || '#000000',
                  ),
              stroke: isLocatedComponent
                ? '#ff0000' // Red stroke for located component drawings
                : isSelected
                  ? '#007bff' // Blue highlight for selected drawing
                  : makeBrighterForBorder(
                      component?.color || config.fill || '#000000',
                    ),
              strokeWidth: isLocatedComponent
                ? Math.max(
                    5 / zoom,
                    parseFloat(config.strokeWidth || '1') / zoom,
                  ) // Extra thick stroke for located component
                : isSelected
                  ? Math.max(
                      3 / zoom,
                      parseFloat(config.strokeWidth || '1') / zoom,
                    ) // Thicker stroke when selected
                  : parseFloat(config.strokeWidth || '1') / zoom,
              draggable:
                selectedTool === 'select' &&
                isSelected &&
                !isIndividualDragDisabled &&
                isEditMode, // Allow dragging in pan mode or select mode when selected, but disable individual dragging when multiple are selected
              onClick: () => handleDrawingSelect(apiDrawing.id),
              onTap: () => handleDrawingSelect(apiDrawing.id), // For touch devices
              onContextMenu: (e: Konva.KonvaEventObject<PointerEvent>) => {
                e.evt.preventDefault();
                handleDrawingContextMenu?.(apiDrawing, e);
              },
              onMouseEnter: (e: Konva.KonvaEventObject<MouseEvent>) => {
                // Change cursor to move when hovering over selected drawings in select mode
                if (selectedTool === 'select' && isSelected && isEditMode) {
                  const stage = e.target.getStage();
                  if (stage) {
                    stage.container().style.cursor = 'move';
                  }
                }

                // Show tooltip on hover
                handleDrawingHover?.(apiDrawing, e);
              },
              onMouseMove: (e: Konva.KonvaEventObject<MouseEvent>) => {
                // Update tooltip position on mouse move
                handleDrawingMouseMove?.(apiDrawing, e);
              },
              onMouseLeave: (e: Konva.KonvaEventObject<MouseEvent>) => {
                // Reset cursor when leaving selected drawings
                if (selectedTool === 'select' && isSelected) {
                  const stage = e.target.getStage();
                  if (stage) {
                    stage.container().style.cursor = 'default';
                  }
                }

                // Hide tooltip on mouse leave
                handleDrawingHoverEnd?.();
              },
              onDragStart: () => {
                if (
                  selectedTool === 'select' &&
                  isSelected &&
                  !isIndividualDragDisabled &&
                  isEditMode
                ) {
                  setIsShapeDragging(true);
                }
              },
              onDragEnd: (e: Konva.KonvaEventObject<DragEvent>) =>
                handleDragEnd(apiDrawing.id, e),
            };

            if (config.type === 'rectangle') {
              return (
                <Rect
                  key={String(apiId)}
                  {...commonShapeProps}
                  width={parseFloat(config.width || '0')}
                  height={parseFloat(config.height || '0')}
                />
              );
            }

            if (config.type === 'freehand') {
              const points = JSON.parse(config.points || '[]');
              const baseX = parseFloat(config.x || '0');
              const baseY = parseFloat(config.y || '0');

              // Convert absolute points to relative points for dragging to work
              const relativePoints = [];
              for (let i = 0; i < points.length; i += 2) {
                relativePoints.push(points[i] - baseX);
                relativePoints.push(points[i + 1] - baseY);
              }

              return (
                <Line
                  key={String(apiId)}
                  {...commonShapeProps}
                  points={relativePoints}
                  closed={config.closed === 'true'}
                  tension={parseFloat(config.tension || '0.3')}
                  lineCap="round"
                  lineJoin="round"
                />
              );
            }

            if (config.type === 'curve') {
              const points = JSON.parse(config.points || '[]');
              const baseX = parseFloat(config.x || '0');
              const baseY = parseFloat(config.y || '0');

              // Convert absolute points to relative points for dragging
              const relativePoints = [];
              for (let i = 0; i < points.length; i += 2) {
                relativePoints.push(points[i] - baseX);
                relativePoints.push(points[i + 1] - baseY);
              }

              return (
                <Line
                  key={String(apiId)}
                  {...commonShapeProps}
                  points={relativePoints}
                  tension={parseFloat(config.tension || '0.3')}
                  lineCap="round"
                  lineJoin="round"
                  closed={config.closed === 'true'}
                  fill={
                    config.closed === 'true'
                      ? makeLighterForDisplay(config.fill)
                      : 'transparent'
                  }
                />
              );
            }

            if (config.type === 'point-to-point') {
              const points = JSON.parse(config.points || '[]');
              const baseX = parseFloat(config.x || '0');
              const baseY = parseFloat(config.y || '0');

              // Convert absolute points to relative points for dragging
              const relativePoints = [];
              for (let i = 0; i < points.length; i += 2) {
                relativePoints.push(points[i] - baseX);
                relativePoints.push(points[i + 1] - baseY);
              }

              return (
                <Line
                  key={String(apiId)}
                  {...commonShapeProps}
                  points={relativePoints}
                  closed={config.closed === 'true'}
                  lineCap="round"
                  lineJoin="round"
                  tension={0} // No tension for straight lines
                />
              );
            }

            if (config.type === 'arrow') {
              const points = JSON.parse(config.points || '[]');
              const baseX = parseFloat(config.x || '0');
              const baseY = parseFloat(config.y || '0');
              const pointerLength = parseFloat(
                config.pointerLength || String(ARROW_POINTER.LENGTH),
              );
              const pointerWidth = parseFloat(
                config.pointerWidth || String(ARROW_POINTER.WIDTH),
              );

              // Convert absolute points to relative points for dragging
              const relativePoints = [];
              for (let i = 0; i < points.length; i += 2) {
                relativePoints.push(points[i] - baseX);
                relativePoints.push(points[i + 1] - baseY);
              }

              return (
                <Arrow
                  key={String(apiId)}
                  {...commonShapeProps}
                  points={relativePoints}
                  pointerLength={pointerLength / zoom}
                  pointerWidth={pointerWidth / zoom}
                  fill={config.stroke || 'black'}
                  stroke={config.stroke || 'black'}
                />
              );
            }

            if (config.type === 'ellipse') {
              return (
                <KonvaEllipse
                  key={String(apiId)}
                  {...commonShapeProps}
                  radiusX={parseFloat(config.radiusX || '0')}
                  radiusY={parseFloat(config.radiusY || '0')}
                />
              );
            }

            if ((config.type as string) === 'point') {
              const points = JSON.parse(config.points || '[]');
              const baseX = parseFloat(config.x || '0');
              const baseY = parseFloat(config.y || '0');

              // Convert absolute points to relative points (no scaling needed - radius is already stored in config)
              const relativePoints = [];
              for (let i = 0; i < points.length; i += 2) {
                const relativeX = points[i] - baseX;
                const relativeY = points[i + 1] - baseY;
                relativePoints.push(relativeX);
                relativePoints.push(relativeY);
              }

              return (
                <Line
                  key={String(apiId)}
                  {...commonShapeProps}
                  points={relativePoints}
                  closed={config.closed === 'true'}
                  lineCap="round"
                  lineJoin="round"
                />
              );
            }

            // Default to circle for backward compatibility
            return (
              <KonvaCircle
                key={String(apiId)}
                {...commonShapeProps}
                radius={parseFloat(config.radius || '0')}
              />
            );
          })}
      </>

      <>
        {currentDrawing &&
          (() => {
            const shape = currentDrawing;
            const commonProps = {
              stroke: makeBrighterForBorder(shape.fill),
              strokeWidth: shape.strokeWidth / zoom,
              dash: [6 / zoom, 3 / zoom],
            };

            if (shape.type === 'rectangle') {
              return (
                <Rect
                  key={`${shape.id}-current`}
                  x={shape.x}
                  y={shape.y}
                  width={shape.width}
                  height={shape.height}
                  fill={makeLighterForDisplay(shape.fill)}
                  {...commonProps}
                />
              );
            }

            if (shape.type === 'circle') {
              return (
                <KonvaCircle
                  key={`${shape.id}-current`}
                  x={shape.x}
                  y={shape.y}
                  radius={shape.radius}
                  fill={makeLighterForDisplay(shape.fill)}
                  {...commonProps}
                />
              );
            }

            if (shape.type === 'ellipse') {
              return (
                <KonvaEllipse
                  key={`${shape.id}-current`}
                  x={shape.x}
                  y={shape.y}
                  radiusX={shape.radiusX}
                  radiusY={shape.radiusY}
                  fill={makeLighterForDisplay(shape.fill)}
                  {...commonProps}
                />
              );
            }

            if (shape.type === 'freehand') {
              // Convert absolute points to relative points for consistency with saved drawings
              const relativePoints = [];
              for (let i = 0; i < shape.points.length; i += 2) {
                relativePoints.push(shape.points[i] - shape.x);
                relativePoints.push(shape.points[i + 1] - shape.y);
              }

              return (
                <Line
                  key={`${shape.id}-current`}
                  x={shape.x}
                  y={shape.y}
                  points={relativePoints}
                  closed={shape.closed}
                  tension={shape.tension}
                  stroke={makeBrighterForBorder(shape.stroke)}
                  strokeWidth={shape.strokeWidth / zoom}
                  fill={
                    shape.closed
                      ? makeLighterForDisplay(shape.fill)
                      : 'transparent'
                  }
                  lineCap="round"
                  lineJoin="round"
                  dash={[6 / zoom, 3 / zoom]}
                />
              );
            }

            if (shape.type === 'curve') {
              return (
                <>
                  {shape.previewPoints.length >= 4 &&
                    (() => {
                      // Convert absolute preview points to relative points
                      const relativePreviewPoints = [];
                      for (let i = 0; i < shape.previewPoints.length; i += 2) {
                        relativePreviewPoints.push(
                          shape.previewPoints[i] - shape.x,
                        );
                        relativePreviewPoints.push(
                          shape.previewPoints[i + 1] - shape.y,
                        );
                      }

                      return (
                        <Line
                          key={`${shape.id}-complete-preview`}
                          x={shape.x}
                          y={shape.y}
                          points={relativePreviewPoints}
                          tension={shape.tension}
                          stroke={makeBrighterForBorder(shape.stroke)}
                          strokeWidth={shape.strokeWidth / zoom}
                          lineCap="round"
                          lineJoin="round"
                          dash={[6 / zoom, 3 / zoom]}
                          opacity={0.8} // Slightly transparent for preview
                          closed={shape.closed}
                          fill={
                            shape.closed
                              ? makeLighterForDisplay(shape.fill)
                              : 'transparent'
                          }
                        />
                      );
                    })()}

                  {/* Fallback: Show confirmed curve if no preview (shouldn't happen during drawing) */}
                  {shape.previewPoints.length === 0 &&
                    shape.points.length >= 4 &&
                    (() => {
                      const relativePoints = [];
                      for (let i = 0; i < shape.points.length; i += 2) {
                        relativePoints.push(shape.points[i] - shape.x);
                        relativePoints.push(shape.points[i + 1] - shape.y);
                      }

                      return (
                        <Line
                          key={`${shape.id}-confirmed`}
                          x={shape.x}
                          y={shape.y}
                          points={relativePoints}
                          tension={shape.tension}
                          stroke={makeBrighterForBorder(shape.stroke)}
                          strokeWidth={shape.strokeWidth / zoom}
                          lineCap="round"
                          lineJoin="round"
                          dash={[6 / zoom, 3 / zoom]}
                          closed={shape.closed}
                          fill={
                            shape.closed
                              ? makeLighterForDisplay(shape.fill)
                              : 'transparent'
                          }
                        />
                      );
                    })()}

                  {/* Anchor point indicators */}
                  {shape.points.map((_, index) => {
                    if (index % 2 === 0) {
                      // Only render for x coordinates
                      const x = shape.points[index];
                      const y = shape.points[index + 1];
                      return (
                        <KonvaCircle
                          key={`anchor-${index / 2}`}
                          x={x}
                          y={y}
                          radius={5 / zoom}
                          fill={makeBrighterForBorder(shape.stroke)}
                          stroke="white"
                          strokeWidth={1 / zoom}
                        />
                      );
                    }
                    return null;
                  })}

                  {/* Current mouse position indicator */}
                  {shape.previewPoints.length >= 2 && shape.isDrawing && (
                    <KonvaCircle
                      key="mouse-indicator"
                      x={shape.previewPoints[shape.previewPoints.length - 2]}
                      y={shape.previewPoints[shape.previewPoints.length - 1]}
                      radius={2 / zoom}
                      fill="transparent"
                      stroke={makeBrighterForBorder(shape.stroke)}
                      strokeWidth={1 / zoom}
                      dash={[2 / zoom, 2 / zoom]}
                    />
                  )}
                </>
              );
            }

            if (shape.type === 'point-to-point') {
              return (
                <>
                  {/* Show straight line segments preview */}
                  {shape.previewPoints.length >= 4 &&
                    (() => {
                      // Convert absolute preview points to relative points
                      const relativePreviewPoints = [];
                      for (let i = 0; i < shape.previewPoints.length; i += 2) {
                        relativePreviewPoints.push(
                          shape.previewPoints[i] - shape.x,
                        );
                        relativePreviewPoints.push(
                          shape.previewPoints[i + 1] - shape.y,
                        );
                      }

                      return (
                        <Line
                          key={`${shape.id}-complete-preview`}
                          x={shape.x}
                          y={shape.y}
                          points={relativePreviewPoints}
                          tension={0} // No tension for straight lines
                          stroke={makeBrighterForBorder(shape.stroke)}
                          strokeWidth={shape.strokeWidth / zoom}
                          lineCap="round"
                          lineJoin="round"
                          dash={[6 / zoom, 3 / zoom]}
                          opacity={0.8}
                          closed={false} // Don't close preview line in rendering
                          fill={
                            shape.closed
                              ? makeLighterForDisplay(shape.fill)
                              : 'transparent'
                          }
                        />
                      );
                    })()}

                  {shape.closed &&
                    shape.points.length >= 4 &&
                    shape.previewPoints.length >= 4 && (
                      <Line
                        key={`${shape.id}-closing-line`}
                        points={[
                          shape.previewPoints[shape.previewPoints.length - 2] -
                            shape.x, // mouse x relative
                          shape.previewPoints[shape.previewPoints.length - 1] -
                            shape.y, // mouse y relative
                          shape.points[0] - shape.x, // start x relative
                          shape.points[1] - shape.y, // start y relative
                        ]}
                        x={shape.x}
                        y={shape.y}
                        stroke={makeBrighterForBorder(shape.stroke)}
                        strokeWidth={shape.strokeWidth / zoom}
                        dash={[3 / zoom, 6 / zoom]} // Different dash pattern for closing line
                        opacity={0.6}
                        lineCap="round"
                      />
                    )}

                  {shape.previewPoints.length === 0 &&
                    shape.points.length >= 4 &&
                    (() => {
                      const relativePoints = [];
                      for (let i = 0; i < shape.points.length; i += 2) {
                        relativePoints.push(shape.points[i] - shape.x);
                        relativePoints.push(shape.points[i + 1] - shape.y);
                      }

                      return (
                        <Line
                          key={`${shape.id}-confirmed`}
                          x={shape.x}
                          y={shape.y}
                          points={relativePoints}
                          tension={0}
                          stroke={makeBrighterForBorder(shape.stroke)}
                          strokeWidth={shape.strokeWidth / zoom}
                          lineCap="round"
                          lineJoin="round"
                          dash={[6 / zoom, 3 / zoom]}
                          closed={shape.closed}
                          fill={
                            shape.closed
                              ? makeLighterForDisplay(shape.fill)
                              : 'transparent'
                          }
                        />
                      );
                    })()}

                  {shape.points.map((_, index) => {
                    if (index % 2 === 0) {
                      // Only render for x coordinates
                      const x = shape.points[index];
                      const y = shape.points[index + 1];
                      return (
                        <KonvaCircle
                          key={`anchor-${index / 2}`}
                          x={x}
                          y={y}
                          radius={5 / zoom}
                          fill={makeBrighterForBorder(shape.stroke)}
                          stroke="white"
                          strokeWidth={1 / zoom}
                        />
                      );
                    }
                    return null;
                  })}

                  {/* Current mouse position indicator */}
                  {shape.previewPoints.length >= 2 && shape.isDrawing && (
                    <KonvaCircle
                      key="mouse-indicator"
                      x={shape.previewPoints[shape.previewPoints.length - 2]}
                      y={shape.previewPoints[shape.previewPoints.length - 1]}
                      radius={2 / zoom}
                      fill="transparent"
                      stroke={makeBrighterForBorder(shape.stroke)}
                      strokeWidth={1 / zoom}
                      dash={[2 / zoom, 2 / zoom]}
                    />
                  )}
                </>
              );
            }

            if (shape.type === 'arrow') {
              return (
                <>
                  {/* Show arrow preview */}
                  {shape.previewPoints.length >= 4 &&
                    (() => {
                      // Convert absolute preview points to relative points
                      const relativePreviewPoints = [];
                      for (let i = 0; i < shape.previewPoints.length; i += 2) {
                        relativePreviewPoints.push(
                          shape.previewPoints[i] - shape.x,
                        );
                        relativePreviewPoints.push(
                          shape.previewPoints[i + 1] - shape.y,
                        );
                      }

                      return (
                        <Arrow
                          key={`${shape.id}-complete-preview`}
                          x={shape.x}
                          y={shape.y}
                          points={relativePreviewPoints}
                          pointerLength={shape.pointerLength / zoom}
                          pointerWidth={shape.pointerWidth / zoom}
                          fill={shape.stroke}
                          stroke={shape.stroke}
                          strokeWidth={shape.strokeWidth / zoom}
                          dash={[6 / zoom, 3 / zoom]}
                          opacity={0.8}
                        />
                      );
                    })()}

                  {/* Fallback: Show confirmed arrow if no preview */}
                  {shape.previewPoints.length === 0 &&
                    shape.points.length >= 4 &&
                    (() => {
                      const relativePoints = [];
                      for (let i = 0; i < shape.points.length; i += 2) {
                        relativePoints.push(shape.points[i] - shape.x);
                        relativePoints.push(shape.points[i + 1] - shape.y);
                      }

                      return (
                        <Arrow
                          key={`${shape.id}-confirmed`}
                          x={shape.x}
                          y={shape.y}
                          points={relativePoints}
                          pointerLength={shape.pointerLength / zoom}
                          pointerWidth={shape.pointerWidth / zoom}
                          fill={shape.stroke}
                          stroke={shape.stroke}
                          strokeWidth={shape.strokeWidth / zoom}
                          dash={[6 / zoom, 3 / zoom]}
                        />
                      );
                    })()}

                  {/* Anchor point indicators */}
                  {shape.points.map((_, index) => {
                    if (index % 2 === 0) {
                      // Only render for x coordinates
                      const x = shape.points[index];
                      const y = shape.points[index + 1];
                      return (
                        <KonvaCircle
                          key={`anchor-${index / 2}`}
                          x={x}
                          y={y}
                          radius={5 / zoom}
                          fill={shape.stroke}
                          stroke="white"
                          strokeWidth={1 / zoom}
                        />
                      );
                    }
                    return null;
                  })}

                  {/* Current mouse position indicator */}
                  {shape.previewPoints.length >= 2 && shape.isDrawing && (
                    <KonvaCircle
                      key="mouse-indicator"
                      x={shape.previewPoints[shape.previewPoints.length - 2]}
                      y={shape.previewPoints[shape.previewPoints.length - 1]}
                      radius={2 / zoom}
                      fill="transparent"
                      stroke={shape.stroke}
                      strokeWidth={1 / zoom}
                      dash={[2 / zoom, 2 / zoom]}
                    />
                  )}
                </>
              );
            }

            if (shape.type === 'point') {
              // Convert absolute points to relative points (current drawing already has correct size from component radius)
              const relativePoints = [];
              for (let i = 0; i < shape.points.length; i += 2) {
                relativePoints.push(shape.points[i] - shape.x);
                relativePoints.push(shape.points[i + 1] - shape.y);
              }

              return (
                <Line
                  key={`${shape.id}-current`}
                  x={shape.x}
                  y={shape.y}
                  points={relativePoints}
                  closed={shape.closed}
                  fill={makeLighterForDisplay(shape.fill)}
                  stroke={makeBrighterForBorder(shape.stroke)}
                  strokeWidth={shape.strokeWidth / zoom}
                  lineCap="round"
                  lineJoin="round"
                  dash={[6 / zoom, 3 / zoom]}
                />
              );
            }

            if (shape.type === 'selection') {
              return (
                <Rect
                  key={`${shape.id}-selection`}
                  x={shape.x}
                  y={shape.y}
                  width={shape.width}
                  height={shape.height}
                  fill={shape.fill}
                  stroke={shape.stroke}
                  strokeWidth={shape.strokeWidth / zoom}
                  dash={[4 / zoom, 4 / zoom]}
                />
              );
            }

            return null;
          })()}
      </>

      {/* Live measurements while drawing */}
      {selectedImage?.scale && paperSize && (
        <LiveMeasurement
          currentDrawing={currentDrawing}
          scale={selectedImage.scale}
          paperSize={paperSize}
          zoom={zoom}
        />
      )}

      {/* Persistent measurements for measurement drawings */}
      {selectedImage?.scale && paperSize && (
        <>
          {fetchedDrawings
            .filter((drawing) => drawing.componentId === null) // Only measurement drawings
            .map((drawing) => (
              <PersistentMeasurement
                key={`persistent-measurement-${drawing.id}`}
                drawing={drawing}
                scale={selectedImage.scale}
                paperSize={paperSize}
                zoom={zoom}
              />
            ))}
        </>
      )}

      {/* Selection bounds for multi-selection */}
      {selectedDrawingIds.length > 1 && (
        <SelectionBounds
          selectedDrawings={fetchedDrawings.filter((drawing) =>
            selectedDrawingIds.includes(drawing.id),
          )}
          zoom={zoom}
          selectedTool={selectedTool}
          groupDragOffset={groupDragOffset}
          onContextMenu={handleSelectionBoundsContextMenu}
          onGroupDragStart={handleGroupDragStart}
          onGroupDragMove={handleGroupDragMove}
          onGroupDragEnd={handleGroupDragEnd}
        />
      )}

      {/* Comments rendered on top of all other drawings */}
      {isCanvasLoaded &&
        !drawingsError &&
        fetchedDrawings
          .filter((drawing) => drawing.config.type === 'comment')
          .map((apiDrawing) => {
            const { config, id: apiId } = apiDrawing;
            const x = parseFloat(config.x || '0');
            const y = parseFloat(config.y || '0');
            const text = config.text || '';

            // Check if this drawing is selected
            const isSelected =
              selectedDrawingId === apiId || selectedDrawingIds.includes(apiId);

            return (
              <CommentAnnotation
                key={String(apiId)}
                x={x}
                y={y}
                text={text}
                id={apiId}
                isSelected={isSelected}
                onSelect={handleDrawingSelect}
                onClick={handleDrawingSelect}
                onContextMenu={(e: any) => {
                  e.evt.preventDefault();
                  handleDrawingContextMenu?.(apiDrawing, e);
                }}
              />
            );
          })}
    </Layer>
  );
}
