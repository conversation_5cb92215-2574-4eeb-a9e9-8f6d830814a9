'use client';

import React, { RefObject } from 'react';
import Image from 'next/image';
import { ArrowUp, Square, Paperclip, X, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { AIModel, AI_MODEL_OPTIONS, FilePreview } from './types/chatbot-types';

interface ChatbotInputProps {
  input: string;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleSubmit: (e: React.FormEvent) => void;
  isLoading: boolean;
  disabled?: boolean;
  selectedModel: AIModel;
  onModelChange: (model: AIModel) => void;
  onStop?: () => void;
  isStreaming?: boolean;
  textareaRef?: RefObject<HTMLTextAreaElement | null>;
  // File attachment props
  files?: FileList;
  filePreviews: FilePreview[];
  onFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onRemoveFile: (index: number) => void;
  fileInputRef: RefObject<HTMLInputElement>;
  maxFiles?: number;
}

// File preview component
const FilePreviewComponent: React.FC<{
  preview: FilePreview;
  index: number;
  onRemove: (index: number) => void;
}> = ({ preview, index, onRemove }) => {
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="relative group">
      <div className="w-16 h-16 rounded-lg overflow-hidden border-2 border-gray-200 hover:border-gray-300 transition-colors">
        {preview.type === 'image' ? (
          <Image
            src={preview.url}
            alt={`Preview ${index}`}
            width={64}
            height={64}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-red-50 flex flex-col items-center justify-center">
            <FileText className="w-4 h-4 text-red-500 mb-1" />
            <span className="text-xs text-red-600 font-medium">PDF</span>
          </div>
        )}
      </div>

      {/* File info */}
      <div className="absolute -bottom-8 left-0 right-0 text-center">
        <div className="text-xs text-gray-600 truncate px-1">
          {preview.file.name.length > 10
            ? preview.file.name.substring(0, 10) + '...'
            : preview.file.name}
        </div>
        <div className="text-xs text-gray-400">
          {formatFileSize(preview.file.size)}
        </div>
      </div>

      {/* Remove button */}
      <button
        type="button"
        onClick={() => onRemove(index)}
        className="absolute -top-2 -right-2 w-5 h-5 bg-red-500 hover:bg-red-600 rounded-full 
                   flex items-center justify-center text-white shadow-lg transition-colors
                   opacity-0 group-hover:opacity-100"
        aria-label="Remove file"
      >
        <X className="w-3 h-3" />
      </button>
    </div>
  );
};

export const ChatbotInput: React.FC<ChatbotInputProps> = ({
  input,
  handleInputChange,
  handleSubmit,
  isLoading,
  disabled = false,
  selectedModel,
  onModelChange,
  onStop,
  isStreaming = false,
  textareaRef,
  files: _files,
  filePreviews,
  onFileChange,
  onRemoveFile,
  fileInputRef,
  maxFiles = 2,
}) => {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (
        (input.trim() || filePreviews.length > 0) &&
        !isLoading &&
        !disabled
      ) {
        handleSubmit(e);
      }
    }
  };

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    handleInputChange(e);

    // Auto-resize textarea
    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
  };

  const handleButtonClick = (e: React.FormEvent) => {
    if (isStreaming && onStop) {
      onStop();
    } else {
      handleSubmit(e);
    }
  };

  const isSubmitDisabled =
    (!input.trim() && filePreviews.length === 0) || isLoading || disabled;
  const showStopButton = isStreaming && onStop;

  return (
    <div className="px-3 pb-3 bg-background">
      <form
        onSubmit={showStopButton ? (e) => e.preventDefault() : handleSubmit}
      >
        {/* File Previews */}
        {filePreviews.length > 0 && (
          <div className="bg-white rounded-xl p-3 border border-gray-200 shadow-sm mb-3">
            <div className="flex items-center gap-2 mb-2">
              <Paperclip className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                Attached Files ({filePreviews.length}/{maxFiles})
              </span>
            </div>
            <div className="flex flex-wrap gap-3 pb-4">
              {filePreviews.map((preview, index) => (
                <FilePreviewComponent
                  key={index}
                  preview={preview}
                  index={index}
                  onRemove={onRemoveFile}
                />
              ))}
            </div>
          </div>
        )}

        {/* Big container matching the UI */}
        <div className="bg-white border border-gray-200 rounded-3xl p-4 shadow-sm hover:shadow-md transition-all duration-200">
          {/* Input area - further reduced height */}
          <div className="mb-3">
            <Textarea
              ref={textareaRef}
              value={input}
              onChange={handleTextareaChange}
              onKeyDown={handleKeyDown}
              placeholder="Ask ai anything about the drawing or attach files"
              disabled={isLoading || disabled}
              className={cn(
                'min-h-[18px] max-h-[120px] resize-none text-base border-0 p-0',
                'focus-visible:ring-0 focus-visible:ring-offset-0 shadow-none',
                'placeholder:text-gray-400 bg-transparent w-full',
                'scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent',
              )}
              rows={1}
            />
          </div>

          {/* Bottom row: Model selector left, Attach button center, Submit button right */}
          <div className="flex items-center justify-between">
            {/* Model selector on the left - compact size */}
            <div className="flex-shrink-0">
              <Select value={selectedModel} onValueChange={onModelChange}>
                <SelectTrigger className="w-[110px] h-8 text-xs border-0 bg-gray-50 hover:bg-gray-100 rounded-lg shadow-none focus:ring-0 focus:ring-offset-0">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="rounded-lg border shadow-lg min-w-[130px]">
                  {AI_MODEL_OPTIONS.map((option) => (
                    <SelectItem
                      key={option.value}
                      value={option.value}
                      className="text-xs rounded focus:bg-primary/10"
                    >
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Action buttons on the right */}
            <div className="flex items-center gap-2">
              {/* Attach Files Button */}
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => fileInputRef.current?.click()}
                disabled={filePreviews.length >= maxFiles}
                className="h-8 w-8 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg 
                           transition-colors disabled:opacity-50 disabled:cursor-not-allowed
                           disabled:hover:bg-transparent"
                title={
                  filePreviews.length >= maxFiles
                    ? 'Maximum files reached'
                    : 'Attach files'
                }
              >
                <Paperclip className="w-4 h-4" />
              </Button>

              {/* Submit button - fully round and small */}
              <Button
                type={showStopButton ? 'button' : 'submit'}
                size="icon"
                disabled={showStopButton ? false : isSubmitDisabled}
                onClick={showStopButton ? handleButtonClick : undefined}
                className={cn(
                  'h-10 w-10 rounded-full transition-all duration-200 cursor-pointer',
                  showStopButton
                    ? 'shadow-sm hover:shadow-md'
                    : isSubmitDisabled
                      ? 'bg-gray-200 text-gray-400 cursor-not-allowed hover:bg-gray-200'
                      : 'bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm hover:shadow-md',
                )}
              >
                {showStopButton ? (
                  <Square className="h-4 w-4 bg-white" />
                ) : (
                  <ArrowUp className="h-5 w-5" />
                )}
              </Button>
            </div>
          </div>

          {/* File size info */}
          <div className="mt-2">
            <div className="text-xs text-gray-500">
              Maximum {maxFiles} files, 4MB each. Supports images and PDFs.
            </div>
          </div>
        </div>

        {/* Hidden File Input */}
        <input
          hidden
          type="file"
          onChange={onFileChange}
          multiple
          ref={fileInputRef}
          accept="image/*,application/pdf"
        />
      </form>
    </div>
  );
};
