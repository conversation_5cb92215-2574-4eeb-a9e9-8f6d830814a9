import React from 'react';

/**
 * Simple regex-based markdown parser for basic formatting
 * Supports: **bold**, ### headings, and - lists
 */
export function parseSimpleMarkdown(text: string): React.ReactNode[] {
  const lines = text.split('\n');
  const elements: React.ReactNode[] = [];

  lines.forEach((line, index) => {
    const trimmedLine = line.trim();

    // Skip empty lines
    if (!trimmedLine) {
      elements.push(<br key={`br-${index}`} />);
      return;
    }

    // Parse headings (### heading)
    const headingMatch = trimmedLine.match(/^### (.+)$/);
    if (headingMatch) {
      const headingText = parseBoldText(headingMatch[1]);
      elements.push(
        <h3
          key={`h3-${index}`}
          className="text-sm font-medium mb-1 mt-2 first:mt-0"
        >
          {headingText}
        </h3>,
      );
      return;
    }

    // Parse list items (- item)
    const listMatch = trimmedLine.match(/^- (.+)$/);
    if (listMatch) {
      const listText = parseBoldText(listMatch[1]);
      elements.push(
        <div key={`li-${index}`} className="flex items-start gap-2 mb-1">
          <span className="text-xs mt-1">•</span>
          <span className="flex-1">{listText}</span>
        </div>,
      );
      return;
    }

    // Parse regular paragraphs with bold text
    const paragraphText = parseBoldText(trimmedLine);
    elements.push(
      <p key={`p-${index}`} className="mb-2 last:mb-0 leading-relaxed">
        {paragraphText}
      </p>,
    );
  });

  return elements;
}

/**
 * Parse bold text (**text**) within a string
 */
function parseBoldText(text: string): React.ReactNode[] {
  const parts: React.ReactNode[] = [];
  const boldRegex = /\*\*(.*?)\*\*/g;
  let lastIndex = 0;
  let match;

  while ((match = boldRegex.exec(text)) !== null) {
    // Add text before the bold part
    if (match.index > lastIndex) {
      parts.push(text.slice(lastIndex, match.index));
    }

    // Add the bold part
    parts.push(
      <strong key={`bold-${match.index}`} className="font-semibold">
        {match[1]}
      </strong>,
    );

    lastIndex = match.index + match[0].length;
  }

  // Add remaining text after the last bold part
  if (lastIndex < text.length) {
    parts.push(text.slice(lastIndex));
  }

  // If no bold text was found, return the original text
  return parts.length === 0 ? [text] : parts;
}
