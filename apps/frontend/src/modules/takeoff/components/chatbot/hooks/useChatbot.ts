import {
  useCallback,
  useMemo,
  useState,
  RefObject,
  useEffect,
  useRef,
} from 'react';
import { useChat } from '@ai-sdk/react';
import { useTakeoffStore } from '../../../store/takeoff-store';
import { useGetDrawings, useGetTakeoffDetails } from '../../../api/queries';
import {
  BlueprintContext,
  DrawingsContext,
  AIModel,
  FilePreview,
} from '../types/chatbot-types';
import { responseErrorInterceptor } from '@/lib/api-client';
import { useAuthStore } from '@/store/auth-store';

export const useChatbot = (
  takeoffId: string,
  textareaRef?: RefObject<HTMLTextAreaElement | null>,
) => {
  const { selectedImage, selectedFile, setChatbotError } = useTakeoffStore();
  const token = useAuthStore((state) => state.authToken);
  const [selectedModel, setSelectedModel] = useState<AIModel>('openai');

  // File attachment state
  const [files, setFiles] = useState<FileList | undefined>(undefined);
  const [filePreviews, setFilePreviews] = useState<FilePreview[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null as any);

  const MAX_FILES = 2;
  const MAX_FILE_SIZE = 4 * 1024 * 1024; // 4MB

  // Get drawings for current image
  const { data: drawingsData } = useGetDrawings(
    { blueprintImageId: selectedImage?.id || '' },
    { enabled: !!selectedImage?.id },
  );

  // Get takeoff details for system prompt
  const { data: takeoffDetails } = useGetTakeoffDetails(takeoffId);

  // Prepare context data that will be sent with each message
  const contextData = useMemo(() => {
    if (!selectedImage || !selectedFile) return null;

    const blueprintContext: BlueprintContext = {
      imageUrl: selectedImage.path,
      fileName: selectedFile.fileName,
      takeoffId: takeoffId,
      blueprintFileId: selectedFile.id,
      blueprintImageId: selectedImage.id,
    };

    const drawingsContext: DrawingsContext = {
      drawings: drawingsData || [],
      imageUrl: selectedImage.path,
      takeoffId: takeoffId,
      blueprintImageId: selectedImage.id,
    };

    return {
      blueprintContext,
      drawingsContext,
    };
  }, [selectedImage, selectedFile, takeoffId, drawingsData]);

  // Use the full power of useChat hook
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    status,
    error,
    append,
    reload,
    stop,
    setMessages,
    setInput,
  } = useChat({
    api: `${process.env.NEXT_PUBLIC_BASE_URL}/chatbot/chat`,
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: contextData
      ? {
          contextData,
          model: selectedModel,
          systemPrompt: takeoffDetails?.ai_system_prompt,
        }
      : {
          model: selectedModel,
          systemPrompt: takeoffDetails?.ai_system_prompt,
        },
    onError: (error) => {
      const axiosError = {
        response: {
          status: JSON.parse(error.message)?.statusCode,
          data: JSON.parse(error.message),
        },
      };
      console.error(error);
      responseErrorInterceptor(axiosError, handleCustomSubmit);
    },
    onResponse: async (response) => {
      if (!response.ok) {
        setChatbotError('Failed to get response from AI assistant');
      } else {
        setChatbotError(null);
      }
    },
    onFinish: () => {
      setChatbotError(null);
      // Clear files after successful submission
      clearFiles();
      // Focus the input field after chat completion with proper timing
      if (textareaRef?.current) {
        // Use setTimeout to ensure DOM updates are complete
        setTimeout(() => {
          if (textareaRef.current) {
            textareaRef.current.focus();
            // Also ensure cursor is at the end if there's any text
            const length = textareaRef.current.value.length;
            textareaRef.current.setSelectionRange(length, length);
          }
        }, 0);
      }
    },
  });

  // File handling functions
  const handleFileChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      if (event.target.files && event.target.files.length > 0) {
        const totalFiles = filePreviews.length + event.target.files.length;

        if (totalFiles > MAX_FILES) {
          setChatbotError(`You can upload a maximum of ${MAX_FILES} files.`);
          if (fileInputRef.current) {
            fileInputRef.current.value = '';
          }
          return;
        }

        const validFiles: File[] = [];
        let hasInvalidSize = false;
        let hasInvalidType = false;

        Array.from(event.target.files).forEach((file) => {
          if (file.size > MAX_FILE_SIZE) {
            hasInvalidSize = true;
            return;
          }

          if (
            !file.type.startsWith('image/') &&
            file.type !== 'application/pdf'
          ) {
            hasInvalidType = true;
            return;
          }

          validFiles.push(file);
        });

        if (hasInvalidSize) {
          setChatbotError(
            'Some files exceed 4MB limit. Please select smaller files.',
          );
        }
        if (hasInvalidType) {
          setChatbotError('Only image and PDF files are allowed.');
        }

        if (validFiles.length > 0) {
          const dataTransfer = new DataTransfer();

          filePreviews.forEach((preview) => {
            dataTransfer.items.add(preview.file);
          });

          validFiles.forEach((file) => {
            dataTransfer.items.add(file);
          });

          const newPreviews = validFiles.map((file) => ({
            url: URL.createObjectURL(file),
            file,
            type: file.type.startsWith('image/')
              ? ('image' as const)
              : ('pdf' as const),
          }));

          setFilePreviews([...filePreviews, ...newPreviews]);
          setFiles(dataTransfer.files);
          setChatbotError(null);
        }
      }
    },
    [filePreviews, setChatbotError, fileInputRef, MAX_FILES, MAX_FILE_SIZE],
  );

  const removeFile = useCallback(
    (indexToRemove: number) => {
      URL.revokeObjectURL(filePreviews[indexToRemove].url);

      const updatedPreviews = filePreviews.filter(
        (_, index) => index !== indexToRemove,
      );
      setFilePreviews(updatedPreviews);

      if (updatedPreviews.length === 0) {
        setFiles(undefined);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      } else {
        const dataTransfer = new DataTransfer();
        updatedPreviews.forEach((preview) => {
          dataTransfer.items.add(preview.file);
        });
        setFiles(dataTransfer.files);
      }
    },
    [filePreviews, fileInputRef],
  );

  const clearFiles = useCallback(() => {
    filePreviews.forEach((preview) => {
      URL.revokeObjectURL(preview.url);
    });
    setFilePreviews([]);
    setFiles(undefined);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [filePreviews, fileInputRef]);

  // Custom submit handler that includes context validation and file attachments
  const handleCustomSubmit = useCallback(
    (e?: React.FormEvent, options?: { allowEmptySubmit?: boolean }) => {
      e?.preventDefault();

      if (!selectedImage || !selectedFile) {
        setChatbotError('Please select a blueprint image first');
        return;
      }

      if (!contextData) {
        setChatbotError('Context data not available');
        return;
      }

      if (
        !input.trim() &&
        filePreviews.length === 0 &&
        !options?.allowEmptySubmit
      ) {
        setChatbotError('Please enter a message or attach a file.');
        return;
      }

      setChatbotError(null);

      // Use the standard handleSubmit with context data and experimental_attachments
      handleSubmit(e, {
        data: JSON.parse(JSON.stringify(contextData)),
        experimental_attachments: files,
      });
    },
    [
      selectedImage,
      selectedFile,
      contextData,
      input,
      filePreviews,
      files,
      handleSubmit,
      setChatbotError,
    ],
  );

  // Cleanup object URLs on unmount
  useEffect(() => {
    return () => {
      filePreviews.forEach((preview) => {
        URL.revokeObjectURL(preview.url);
      });
    };
  }, [filePreviews]);

  return {
    // AI SDK managed state
    messages,
    input,
    handleInputChange,
    handleSubmit: handleCustomSubmit,
    isLoading: status === 'streaming' || status === 'submitted',
    status,
    error,

    // Additional utilities
    append,
    reload,
    stop,
    setMessages,
    setInput,

    // Model selection
    selectedModel,
    setSelectedModel,

    // Context validation
    hasValidContext: !!contextData,

    // File attachment state and handlers
    files,
    filePreviews,
    handleFileChange,
    removeFile,
    clearFiles,
    fileInputRef,
    maxFiles: MAX_FILES,
  };
};
