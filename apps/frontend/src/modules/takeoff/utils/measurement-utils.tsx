import { Text, Rect } from 'react-konva';
import { DrawingShape } from '../types/drawing-types';
import {
  ScaleInfo,
  PaperSize,
  calculateDrawingMeasurements,
  DynamicMeasurement,
} from '@repo/component-summary';

// Constants for measurement display (like Figma - consistent regardless of zoom)
export const MEASUREMENT_DISPLAY = {
  FONT_SIZE: 12,
  PADDING_X: 8,
  PADDING_Y: 4,
  CORNER_RADIUS: 4,
  BOTTOM_OFFSET: 16, // Distance from bottom of shape to measurement
  SHADOW_OFFSET: 1,
} as const;

export interface MeasurementBadgeProps {
  text: string;
  x: number;
  y: number;
  zoom: number;
}

/**
 * Reusable measurement badge component
 */
export function MeasurementBadge({ text, x, y, zoom }: MeasurementBadgeProps) {
  // Calculate fixed dimensions (like Figma - independent of zoom)
  const fontSize = MEASUREMENT_DISPLAY.FONT_SIZE / zoom;
  const textWidth = text.length * fontSize * 0.6;
  const textHeight = fontSize;
  const paddingX = MEASUREMENT_DISPLAY.PADDING_X / zoom;
  const paddingY = MEASUREMENT_DISPLAY.PADDING_Y / zoom;
  const cornerRadius = MEASUREMENT_DISPLAY.CORNER_RADIUS / zoom;
  const shadowOffset = MEASUREMENT_DISPLAY.SHADOW_OFFSET / zoom;

  return (
    <>
      {/* Subtle shadow effect */}
      <Rect
        x={x - textWidth / 2 - paddingX + shadowOffset}
        y={y - textHeight / 2 - paddingY + shadowOffset}
        width={textWidth + paddingX * 2}
        height={textHeight + paddingY * 2}
        fill="rgba(0, 0, 0, 0.08)"
        cornerRadius={cornerRadius}
      />

      {/* Main badge background */}
      <Rect
        x={x - textWidth / 2 - paddingX}
        y={y - textHeight / 2 - paddingY}
        width={textWidth + paddingX * 2}
        height={textHeight + paddingY * 2}
        fill="#09090b"
        cornerRadius={cornerRadius}
      />

      {/* Measurement text */}
      <Text
        x={x - textWidth / 2}
        y={y - textHeight / 2}
        text={text}
        fontSize={fontSize}
        fontFamily="Inter, system-ui, sans-serif"
        fontStyle="600"
        fill="#fafafa"
        width={textWidth}
        height={textHeight}
        align="center"
        verticalAlign="middle"
      />
    </>
  );
}

/**
 * Convert DrawingShape to config format for dynamic measurements
 */
export function drawingShapeToConfig(
  drawing: DrawingShape,
): Record<string, string> {
  const config: Record<string, string> = {
    type: drawing.type,
  };

  switch (drawing.type) {
    case 'rectangle':
      config.width = drawing.width.toString();
      config.height = drawing.height.toString();
      break;
    case 'circle':
      config.radius = drawing.radius.toString();
      break;
    case 'ellipse':
      config.radiusX = drawing.radiusX.toString();
      config.radiusY = drawing.radiusY.toString();
      break;
    case 'freehand':
      config.points = JSON.stringify(drawing.points);
      config.closed = drawing.closed.toString();
      if (drawing.thickness !== undefined) {
        config.thickness = drawing.thickness.toString();
      }
      break;
    case 'curve':
      config.points = JSON.stringify(drawing.points);
      config.tension = drawing.tension.toString();
      config.closed = drawing.closed.toString();
      if (drawing.thickness !== undefined) {
        config.thickness = drawing.thickness.toString();
      }
      break;
    case 'point-to-point':
      // Use preview points if available (during drawing), otherwise use confirmed points
      const pointsToUse =
        drawing.previewPoints.length > 0
          ? drawing.previewPoints
          : drawing.points;
      config.points = JSON.stringify(pointsToUse);
      config.closed = drawing.closed.toString();
      if (drawing.thickness !== undefined) {
        config.thickness = drawing.thickness.toString();
      }
      break;
    case 'point':
      config.points = JSON.stringify(drawing.points);
      config.closed = drawing.closed.toString();
      config.pointType = drawing.pointType;
      config.radius = drawing.radius.toString();
      break;
  }

  return config;
}

/**
 * Calculate measurement position for a shape from DrawingShape
 */
export function calculateMeasurementPositionFromShape(
  drawing: DrawingShape,
  zoom: number,
): { x: number; y: number } {
  const bottomOffset = MEASUREMENT_DISPLAY.BOTTOM_OFFSET / zoom;

  switch (drawing.type) {
    case 'rectangle': {
      const rectBottom = Math.max(drawing.y, drawing.y + drawing.height);
      const rectCenterX = drawing.x + drawing.width / 2;
      return { x: rectCenterX, y: rectBottom + bottomOffset };
    }
    case 'circle': {
      const circleBottom = drawing.y + Math.abs(drawing.radius);
      return { x: drawing.x, y: circleBottom + bottomOffset };
    }
    case 'ellipse': {
      const ellipseBottom = drawing.y + Math.abs(drawing.radiusY);
      return { x: drawing.x, y: ellipseBottom + bottomOffset };
    }
    case 'freehand': {
      if (drawing.closed) {
        // Surface component - show at center
        let centerX = 0;
        let centerY = 0;
        for (let i = 0; i < drawing.points.length; i += 2) {
          centerX += drawing.points[i];
          centerY += drawing.points[i + 1];
        }
        centerX /= drawing.points.length / 2;
        centerY /= drawing.points.length / 2;
        return { x: centerX, y: centerY + bottomOffset };
      } else {
        // Edge component - show at end of line
        const lastPointIndex = drawing.points.length - 2;
        return {
          x: drawing.points[lastPointIndex],
          y: drawing.points[lastPointIndex + 1] + bottomOffset,
        };
      }
    }
    case 'curve': {
      // For curves, use previewPoints if available (during drawing), otherwise use points
      const pointsToUse =
        drawing.previewPoints.length > 0
          ? drawing.previewPoints
          : drawing.points;

      // Edge component - show at end of curve
      const lastPointIndex = pointsToUse.length - 2;
      return {
        x: pointsToUse[lastPointIndex],
        y: pointsToUse[lastPointIndex + 1] + bottomOffset,
      };
    }
    case 'point-to-point': {
      const pointsToUse =
        drawing.previewPoints.length > 0
          ? drawing.previewPoints
          : drawing.points;

      if (drawing.closed) {
        // Surface component - show at center
        let centerX = 0;
        let centerY = 0;
        for (let i = 0; i < pointsToUse.length; i += 2) {
          centerX += pointsToUse[i];
          centerY += pointsToUse[i + 1];
        }
        centerX /= pointsToUse.length / 2;
        centerY /= pointsToUse.length / 2;
        return { x: centerX, y: centerY + bottomOffset };
      } else {
        // Edge component - show at end of line
        const lastPointIndex = pointsToUse.length - 2;
        return {
          x: pointsToUse[lastPointIndex],
          y: pointsToUse[lastPointIndex + 1] + bottomOffset,
        };
      }
    }
    case 'point': {
      // For point shapes, show measurement at the center of the point
      // Points are stored as shape coordinates, so we need to find the center
      let centerX = 0;
      let centerY = 0;
      for (let i = 0; i < drawing.points.length; i += 2) {
        centerX += drawing.points[i];
        centerY += drawing.points[i + 1];
      }
      centerX /= drawing.points.length / 2;
      centerY /= drawing.points.length / 2;
      return { x: centerX, y: centerY + bottomOffset };
    }
    case 'comment': {
      // For comments, show measurement at the bottom center of the comment box
      const commentBottom = drawing.y + 100; // Comments have auto-calculated height, estimate
      const commentCenterX = drawing.x + drawing.width / 2;
      return { x: commentCenterX, y: commentBottom + bottomOffset };
    }
    case 'arrow': {
      // For arrows, use previewPoints if available (during drawing), otherwise use points
      const pointsToUse =
        drawing.previewPoints.length > 0
          ? drawing.previewPoints
          : drawing.points;

      // Show measurement at the end point (arrow head)
      const lastPointIndex = pointsToUse.length - 2;
      return {
        x: pointsToUse[lastPointIndex],
        y: pointsToUse[lastPointIndex + 1] + bottomOffset,
      };
    }
    case 'selection':
      // Selection boxes don't show measurements
      return { x: 0, y: 0 };
    default:
      return { x: 0, y: 0 };
  }
}

/**
 * Calculate measurement position for a drawing from config
 */
export function calculateMeasurementPositionFromConfig(
  config: Record<string, string>,
  zoom: number,
): { x: number; y: number } {
  const bottomOffset = MEASUREMENT_DISPLAY.BOTTOM_OFFSET / zoom;
  const type = config.type;

  switch (type) {
    case 'rectangle': {
      const x = parseFloat(config.x || '0');
      const y = parseFloat(config.y || '0');
      const width = parseFloat(config.width || '0');
      const height = parseFloat(config.height || '0');

      const rectBottom = Math.max(y, y + height);
      const rectCenterX = x + width / 2;
      return { x: rectCenterX, y: rectBottom + bottomOffset };
    }
    case 'circle': {
      const x = parseFloat(config.x || '0');
      const y = parseFloat(config.y || '0');
      const radius = parseFloat(config.radius || '0');

      const circleBottom = y + Math.abs(radius);
      return { x: x, y: circleBottom + bottomOffset };
    }
    case 'ellipse': {
      const x = parseFloat(config.x || '0');
      const y = parseFloat(config.y || '0');
      const radiusY = parseFloat(config.radiusY || '0');

      const ellipseBottom = y + Math.abs(radiusY);
      return { x: x, y: ellipseBottom + bottomOffset };
    }
    case 'freehand':
    case 'point-to-point': {
      const points = JSON.parse(config.points || '[]');
      const isClosed = config.closed === 'true';

      if (isClosed) {
        // Surface component - show at center
        let centerX = 0;
        let centerY = 0;
        for (let i = 0; i < points.length; i += 2) {
          centerX += points[i];
          centerY += points[i + 1];
        }
        centerX /= points.length / 2;
        centerY /= points.length / 2;
        return { x: centerX, y: centerY + bottomOffset };
      } else {
        // Edge component - show at end of line
        const lastPointIndex = points.length - 2;
        return {
          x: points[lastPointIndex],
          y: points[lastPointIndex + 1] + bottomOffset,
        };
      }
    }
    case 'curve': {
      const points = JSON.parse(config.points || '[]');
      // Edge component - show at end of curve
      const lastPointIndex = points.length - 2;
      return {
        x: points[lastPointIndex],
        y: points[lastPointIndex + 1] + bottomOffset,
      };
    }
    default:
      return { x: 0, y: 0 };
  }
}

/**
 * Get display text from measurements for DrawingShape
 */
export function getMeasurementTextFromShape(
  measurements: DynamicMeasurement,
  drawing: DrawingShape,
): string {
  // For surface components (closed shapes), show area
  if (
    drawing.type === 'rectangle' ||
    drawing.type === 'circle' ||
    drawing.type === 'ellipse'
  ) {
    return measurements.area?.formatted || '';
  }

  if (drawing.type === 'freehand' || drawing.type === 'point-to-point') {
    if (drawing.closed) {
      return measurements.area?.formatted || '';
    } else {
      return measurements.length?.formatted || '';
    }
  }

  return '';
}

/**
 * Get display text from measurements for config
 */
export function getMeasurementTextFromConfig(
  measurements: DynamicMeasurement,
  config: Record<string, string>,
): string {
  const type = config.type;

  // For surface components (closed shapes), show area
  if (type === 'rectangle' || type === 'circle' || type === 'ellipse') {
    return measurements.area?.formatted || '';
  }

  if (type === 'freehand' || type === 'point-to-point') {
    const isClosed = config.closed === 'true';
    if (isClosed) {
      return measurements.area?.formatted || '';
    } else {
      return measurements.length?.formatted || '';
    }
  }

  if (type === 'curve') {
    return measurements.length?.formatted || '';
  }

  return '';
}

/**
 * Check minimum size requirements for DrawingShape
 */
export function checkMinimumSizeForShape(drawing: DrawingShape): boolean {
  if (drawing.type === 'rectangle') {
    return Math.abs(drawing.width) >= 1 && Math.abs(drawing.height) >= 1;
  } else if (drawing.type === 'circle') {
    return Math.abs(drawing.radius) >= 1;
  } else if (drawing.type === 'ellipse') {
    return Math.abs(drawing.radiusX) >= 1 && Math.abs(drawing.radiusY) >= 1;
  } else if (drawing.type === 'freehand') {
    return drawing.points.length >= 4;
  } else if (drawing.type === 'point-to-point') {
    const pointsToUse =
      drawing.previewPoints.length > 0 ? drawing.previewPoints : drawing.points;
    return pointsToUse.length >= 4;
  }

  return true;
}

/**
 * Check minimum size requirements for config
 */
export function checkMinimumSizeForConfig(
  config: Record<string, string>,
): boolean {
  if (config.type === 'rectangle') {
    const width = Math.abs(parseFloat(config.width || '0'));
    const height = Math.abs(parseFloat(config.height || '0'));
    return width >= 1 && height >= 1;
  } else if (config.type === 'circle') {
    const radius = Math.abs(parseFloat(config.radius || '0'));
    return radius >= 1;
  } else if (config.type === 'ellipse') {
    const radiusX = Math.abs(parseFloat(config.radiusX || '0'));
    const radiusY = Math.abs(parseFloat(config.radiusY || '0'));
    return radiusX >= 1 && radiusY >= 1;
  } else if (
    config.type === 'freehand' ||
    config.type === 'point-to-point' ||
    config.type === 'curve'
  ) {
    const points = JSON.parse(config.points || '[]');
    return points.length >= 4;
  }

  return true;
}

/**
 * Common measurement calculation logic
 */
export function calculateMeasurementData(
  config: Record<string, string>,
  scale: ScaleInfo,
  paperSize: PaperSize,
): {
  measurements: DynamicMeasurement | null;
  displayText: string;
} {
  const measurements = calculateDrawingMeasurements(config, scale, paperSize);

  if (!measurements) {
    return { measurements: null, displayText: '' };
  }

  const displayText = getMeasurementTextFromConfig(measurements, config);

  return { measurements, displayText };
}
