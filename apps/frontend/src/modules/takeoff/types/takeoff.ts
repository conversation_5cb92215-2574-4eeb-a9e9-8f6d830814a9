export interface TakeoffDetails {
  id: number;
  userId: string;
  name: string | null;
  description: string | null;
  submissionDate: string | null;
  quotedPrice: number | null;
  ai_system_prompt: string | null;
  dueDate: string | null;
  status: 'submitted' | 'won' | 'lost';
  conversionStatus: 'processing' | 'completed' | 'failed';
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  blueprintFiles?: BlueprintFile[];
}

export interface BlueprintFile {
  id: string;
  takeoffId: number;
  fileName: string | null;
  fileUrl: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}
