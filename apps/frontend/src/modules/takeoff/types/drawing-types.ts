// Types for canvas drawing functionality
import Konva from 'konva';
import { Drawing } from './drawing';

// Base type for all drawings
export type BaseDrawing = {
  id: string; // Client-side ID, mainly for React keys
  x: number;
  y: number;
  fill: string;
  stroke: string;
  strokeWidth: number; // Store base stroke width, adjust for scale in rendering
};

// Rectangle shape type
export type RectangleShape = BaseDrawing & {
  type: 'rectangle';
  width: number;
  height: number;
};

// Circle shape type
export type CircleShape = BaseDrawing & {
  type: 'circle';
  radius: number;
};

// Ellipse shape type
export type EllipseShape = BaseDrawing & {
  type: 'ellipse';
  radiusX: number;
  radiusY: number;
};

// Freehand shape type
export type FreehandShape = BaseDrawing & {
  type: 'freehand';
  points: number[]; // [x1, y1, x2, y2, x3, y3, ...]
  closed: boolean; // true for surface, false for edge
  tension: number; // 0.3 for smooth curves
  thickness?: number; // The thickness used when creating this edge drawing
};

// Curve shape type
export type CurveShape = BaseDrawing & {
  type: 'curve';
  points: number[]; // [x1, y1, x2, y2, x3, y3, ...] - anchor points
  previewPoints: number[]; // Complete curve including mouse position for preview
  tension: number; // 1 for smooth curves (as per React Konva example)
  closed: boolean; // true for surface, false for edge
  isDrawing?: boolean; // Track if curve is still being drawn
  thickness?: number; // The thickness used when creating this edge drawing
};

// Point-to-point shape type
export type PointToPointShape = BaseDrawing & {
  type: 'point-to-point';
  points: number[]; // [x1, y1, x2, y2, x3, y3, ...] - anchor points
  previewPoints: number[]; // Complete line segments including mouse position for preview
  closed: boolean; // true for surface, false for edge
  isDrawing?: boolean; // Track if drawing is still being drawn
  thickness?: number; // The thickness used when creating this edge drawing
};

// Point shape type - for marking specific spots
export type PointShape = BaseDrawing & {
  type: 'point';
  points: number[]; // Array of points forming the shape [x1, y1, x2, y2, ...]
  closed: boolean; // Always true for point shapes
  pointType: 'circle' | 'square' | 'triangle'; // Shape type for the point
  radius: number; // The radius used when creating this point drawing
};

// Comment shape type - for text annotations
export type CommentShape = BaseDrawing & {
  type: 'comment';
  text: string;
  width: number; // Fixed at 200px, height is auto-calculated
};

// Arrow shape type - for directional annotations
export type ArrowShape = BaseDrawing & {
  type: 'arrow';
  points: number[]; // [x1, y1, x2, y2, x3, y3, ...] - anchor points
  previewPoints: number[]; // Complete arrow path including mouse position for preview
  isDrawing?: boolean; // Track if arrow is still being drawn
  pointerLength: number; // Arrow head length
  pointerWidth: number; // Arrow head width
};

// Selection box shape for multi-selection
export interface SelectionBox {
  id: string;
  type: 'selection';
  x: number;
  y: number;
  width: number;
  height: number;
  fill: string;
  stroke: string;
  strokeWidth: number;
}

// Union type for all drawing shapes
export type DrawingShape =
  | RectangleShape
  | CircleShape
  | EllipseShape
  | FreehandShape
  | CurveShape
  | PointToPointShape
  | PointShape
  | CommentShape
  | ArrowShape
  | SelectionBox;

// Type for the selected drawing tool
export type SelectedTool =
  | 'rectangle'
  | 'circle'
  | 'ellipse'
  | 'freehand'
  | 'curve'
  | 'point-to-point'
  | 'point'
  | 'comment'
  | 'arrow'
  | 'pan'
  | 'select';

// Props for the URLImage component
export interface URLImageProps {
  src: string;
  width: number;
  height: number;
  handleStageClick: (e: Konva.KonvaEventObject<MouseEvent>) => void;
  handleCanvasContextMenu?: (e: Konva.KonvaEventObject<PointerEvent>) => void;
}

// Canvas dimensions type
export interface CanvasDimensions {
  width: number;
  height: number;
}

// Canvas position type
export interface CanvasPosition {
  x: number;
  y: number;
}

// Base interface for all undo/redo actions
interface BaseUndoRedoAction {
  id: string;
  timestamp: number;
}

// Specific action types with proper typing
interface CreateAction extends BaseUndoRedoAction {
  type: 'create';
  data: {
    drawings: Drawing[]; // Always an array, even for single items
  };
}

interface DeleteAction extends BaseUndoRedoAction {
  type: 'delete';
  data: {
    deletedDrawings: Drawing[]; // Always an array, even for single items
  };
}

interface UpdateAction extends BaseUndoRedoAction {
  type: 'update';
  data: {
    drawings: {
      id: number;
      oldData: {
        config: Record<string, string>;
        componentId: number | null;
      };
      newData: {
        config: Record<string, string>;
        componentId: number | null;
      };
    }[]; // Support batch updates
  };
}

interface MoveAction extends BaseUndoRedoAction {
  type: 'move';
  data: {
    drawings: {
      id: number;
      oldData: {
        config: Record<string, string>; // Contains position data
        componentId: number | null;
      };
      newData: {
        config: Record<string, string>; // Contains new position
        componentId: number | null;
      };
    }[]; // Support batch moves
  };
}

interface CutPasteAction extends BaseUndoRedoAction {
  type: 'cut-paste';
  data: {
    originalDrawings: Drawing[]; // Original drawings with their positions
    newDrawings: Drawing[]; // New drawings at pasted positions
  };
}

// Union type for all possible actions
export type UndoRedoAction =
  | CreateAction
  | DeleteAction
  | UpdateAction
  | MoveAction
  | CutPasteAction;

// Type for action types only
export type UndoRedoActionType = UndoRedoAction['type'];
