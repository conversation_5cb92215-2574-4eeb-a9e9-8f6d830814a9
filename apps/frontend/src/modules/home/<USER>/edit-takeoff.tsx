'use client';

import { ErrorMessage } from '@/components/error-message';
import { But<PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';

import { toast } from '@/lib/toast';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as z from 'zod';
import { useUpdateTakeoff } from '../api/mutations';
import { useQueryClient } from '@tanstack/react-query';
import { Datum } from '../types/takeoff';

// Define the Zod schema for form validation
const editTakeoffSchema = z.object({
  name: z
    .string()
    .nonempty('Takeoff name is required')
    .min(2, 'Takeoff name must be at least 2 characters'),
  quotedPrice: z.coerce.number().min(0, 'Price must be a positive number'),
  dueDate: z.date({
    required_error: 'Due date is required',
  }),
  status: z.enum(['submitted', 'won', 'lost']),
  ai_system_prompt: z.string().optional(),
});

// Infer the type from the schema
type EditTakeoffFormValues = z.infer<typeof editTakeoffSchema>;

interface EditTakeoffProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  takeoffData: Datum | null;
}

export const EditTakeoff = ({
  isOpen,
  onOpenChange,
  takeoffData,
}: EditTakeoffProps) => {
  const [open, setOpen] = useState(false);
  const updateTakeoffMutation = useUpdateTakeoff();
  const queryClient = useQueryClient();

  const form = useForm<EditTakeoffFormValues>({
    resolver: zodResolver(editTakeoffSchema),
    defaultValues: {
      name: '',
      quotedPrice: 0,
      dueDate: new Date(),
      status: 'submitted' as const,
      ai_system_prompt: '',
    },
  });

  const {
    formState: { errors },
    reset,
    control,
  } = form;

  // Handle external open state changes
  useEffect(() => {
    setOpen(isOpen);
  }, [isOpen]);

  // Update form values when takeoff data changes
  useEffect(() => {
    if (takeoffData) {
      reset({
        name: takeoffData.name,
        quotedPrice: Number(takeoffData.quotedPrice),
        dueDate: new Date(takeoffData.dueDate),
        status: takeoffData.status as 'submitted' | 'won' | 'lost',
        ai_system_prompt: (takeoffData as any).ai_system_prompt || '',
      });
    }
  }, [takeoffData, reset, isOpen]);

  const onSubmit = async (data: EditTakeoffFormValues) => {
    if (takeoffData) {
      updateTakeoffMutation.mutate(
        {
          id: takeoffData.id,
          name: data.name,
          quotedPrice: data.quotedPrice,
          dueDate: data.dueDate.toISOString(),
          status: data.status,
          ai_system_prompt: data.ai_system_prompt,
        },
        {
          onSuccess: () => {
            toast.success('Takeoff updated successfully');
            setOpen(false);
            onOpenChange(false);
            queryClient.invalidateQueries({ queryKey: ['takeoff'] });
          },
          onError: (error) => {
            toast.error(error);
          },
        },
      );
    }
  };

  // Handle sheet close
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    onOpenChange(newOpen);

    // Reset form when closing
    if (!newOpen) {
      reset({
        name: '',
        quotedPrice: 0,
        dueDate: new Date(),
        status: 'submitted' as const,
        ai_system_prompt: '',
      });
    }
  };

  return (
    <Sheet open={open} onOpenChange={handleOpenChange}>
      <SheetContent
        style={{
          maxWidth: '475px',
        }}
      >
        <form
          className="flex h-full w-full flex-col"
          onSubmit={form.handleSubmit(onSubmit)}
        >
          <SheetHeader className="flex-none">
            <SheetTitle>Edit Takeoff</SheetTitle>
            <SheetDescription>Update takeoff details</SheetDescription>
          </SheetHeader>

          <div className="flex-1 overflow-y-auto" id="form-container">
            <div className="space-y-4 px-4 py-4">
              <div className="flex flex-col gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <div className="col-span-3">
                  <Controller
                    control={control}
                    name="name"
                    render={({ field }) => (
                      <Input
                        id="name"
                        placeholder="Enter takeoff name"
                        {...field}
                        className="w-full"
                      />
                    )}
                  />
                  {errors.name && <ErrorMessage error={errors.name} />}
                </div>
              </div>

              <div className="flex flex-col gap-4">
                <Label htmlFor="quotedPrice" className="text-right">
                  Quoted Price ($)
                </Label>
                <div className="col-span-3">
                  <Controller
                    control={control}
                    name="quotedPrice"
                    render={({ field }) => (
                      <Input
                        id="quotedPrice"
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="Enter quoted price"
                        {...field}
                        className="w-full"
                      />
                    )}
                  />
                  {errors.quotedPrice && (
                    <ErrorMessage error={errors.quotedPrice} />
                  )}
                </div>
              </div>

              <div className="flex flex-col gap-4 mb-8">
                <Label htmlFor="dueDate" className="text-right">
                  Due Date
                </Label>
                <div className="col-span-3">
                  <Controller
                    control={control}
                    name="dueDate"
                    render={({ field }) => (
                      <div className="w-full">
                        <Popover modal={true}>
                          <PopoverTrigger asChild>
                            <Button
                              variant={'outline'}
                              className={cn(
                                'w-full pl-3 text-left font-normal',
                                !field.value && 'text-muted-foreground',
                              )}
                              onClick={(e) => {
                                // Prevent event from propagating to parent elements
                                e.stopPropagation();
                              }}
                            >
                              {field.value ? (
                                format(field.value, 'PPP')
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent
                            className="w-auto p-0 z-[9999]"
                            align="start"
                            sideOffset={5}
                            onInteractOutside={(e) => {
                              // Prevent closing when clicking inside the calendar
                              if (
                                e.target &&
                                (e.target as HTMLElement).closest('.rdp')
                              ) {
                                e.preventDefault();
                              }
                            }}
                          >
                            <div onClick={(e) => e.stopPropagation()}>
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={(date) => {
                                  field.onChange(date);
                                }}
                                initialFocus
                              />
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                    )}
                  />
                  {errors.dueDate && <ErrorMessage error={errors.dueDate} />}
                </div>
              </div>

              <div className="flex flex-col gap-4 mb-8">
                <Label htmlFor="status" className="text-right">
                  Status
                </Label>
                <div className="col-span-3">
                  <Controller
                    control={control}
                    name="status"
                    render={({ field }) => (
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="submitted">Submitted</SelectItem>
                          <SelectItem value="won">Won</SelectItem>
                          <SelectItem value="lost">Lost</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errors.status && <ErrorMessage error={errors.status} />}
                </div>
              </div>

              <div className="flex flex-col gap-4 mb-8">
                <div>
                  <Label htmlFor="ai_system_prompt" className="text-right mb-1">
                    AI System Prompt (Optional)
                  </Label>
                  <span className="text-sm text-muted-foreground">
                    Provide specific instructions for the AI to follow
                  </span>
                </div>
                <div className="col-span-3">
                  <Controller
                    control={control}
                    name="ai_system_prompt"
                    render={({ field }) => (
                      <Textarea
                        id="ai_system_prompt"
                        placeholder="Enter AI system prompt (optional)"
                        {...field}
                        className="w-full min-h-[100px]"
                        rows={4}
                      />
                    )}
                  />
                  {errors.ai_system_prompt && (
                    <ErrorMessage error={errors.ai_system_prompt} />
                  )}
                </div>
              </div>
            </div>
          </div>

          <SheetFooter className="flex-none p-4 grid grid-cols-2 gap-4 w-full border-t bg-white">
            <SheetClose asChild>
              <Button
                type="button"
                variant="outline"
                disabled={updateTakeoffMutation.isPending}
              >
                Cancel
              </Button>
            </SheetClose>
            <Button type="submit" disabled={updateTakeoffMutation.isPending}>
              {updateTakeoffMutation.isPending ? 'Updating...' : 'Update'}
            </Button>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
};
